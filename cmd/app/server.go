package app

import (
	"context"
	"flag"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"

	grpcserver "transwarp.io/mlops/pipeline/api/grpc"
	httpserver "transwarp.io/mlops/pipeline/api/http"
	"transwarp.io/mlops/pipeline/cmd/app/options"
	"transwarp.io/mlops/pipeline/internal/config"
	core "transwarp.io/mlops/pipeline/internal/core/manager"
	dao "transwarp.io/mlops/pipeline/internal/dao"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers"
)

func NewMLOPSPipelineCommand() *cobra.Command {
	s := options.NewServerRunOptions()
	cmd := &cobra.Command{
		Use:   "mlops-pipeline",
		Short: "Controls job and manage compute resources",
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
		},
		RunE: func(cmd *cobra.Command, args []string) error {
			completedOptions, err := s.Complete()
			if err != nil {
				return err
			}

			if errs := completedOptions.Validate(); len(errs) != 0 {
				return utilerrors.NewAggregate(errs)
			}

			return Run(cmd.Context(), completedOptions)
		},
	}

	pflag := cmd.PersistentFlags()
	pflag.Int("port", 8080, "Port for the HTTP server")
	viper.BindPFlags(pflag)

	return cmd
}

func Run(ctx context.Context, opts options.CompletedOptions) error {
	var configPath string
	flag.StringVar(&configPath, "config", "",
		"The server will load its initial configuration from this file. "+
			"Omit this flag to use the default configuration values. ")

	_, _, err := apply(configPath)
	if err != nil {
		zlog.SugarErrorf("Unable to load the configuration: %w", err)
		os.Exit(1)
	}

	dao.Init()
	core.Init()

	// setup task handler
	handlerframework.StartHandlerManagerLeaderElection(ctx)

	// stdlog.Init(conf.PipeineConfig.MLOPS.LogConfig, "pipeline")
	// 3. start server
	// 创建一个 go-restful WebService
	errChan := make(chan error, 2)
	go func() {
		grpc := grpcserver.NewGRPCServer(&grpcserver.Option{
			Port: config.PipeineConfig.MLOPS.GRPCServer.Port,
		})
		ech := grpc.Start(ctx)
		select {
		case e := <-ech:
			errChan <- e
		}
	}()

	go func() {
		http := httpserver.NewHTTPServer(&httpserver.Option{
			Port: config.PipeineConfig.MLOPS.HttpServer.Port,
		})
		ech := http.Start(ctx)
		select {
		case e := <-ech:
			errChan <- e
		}
	}()

	select {
	case err := <-errChan:
		return err
	}
}

func apply(configPath string) (*config.Config, *config.MetricsConfig, error) {
	pipelineConfig, err := config.Load(configPath)
	if err != nil {
		return nil, nil, err
	}

	metricsConfig, err := config.LoadMetricsConfig(configPath)
	if err != nil {
		return nil, nil, err
	}

	zlog.SugarInfoln("Successfully loaded configuration")
	return pipelineConfig, metricsConfig, nil
}
