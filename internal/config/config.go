package config

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"

	"github.com/go-viper/mapstructure/v2"
	"github.com/spf13/viper"
	"transwarp.io/mlops/mlops-std/conf"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	PipeineConfig *Config

	SophonServiceId    string
	pipelineConfigOnce sync.Once
)

func MustGetConfig() *Config {
	pipelineConfigOnce.Do(func() {
		_, err := Load("")
		if err != nil {
			panic(fmt.Sprintf("Failed to load configuration: %v", err))
		}
	})

	if PipeineConfig == nil {
		panic("Failed to load configuration")
	}

	// Return the loaded configuration.
	return PipeineConfig
}

func Load(configPath string) (*Config, error) {
	SophonServiceId = os.Getenv("SOPHON_SERVICE_ID")

	if configPath == "" {
		configPath = os.Getenv("MLOPS_CONF_DIR")
	}
	return loadConfig(configPath)
}

func loadConfig(confPath string) (*Config, error) {
	viper.AutomaticEnv()
	viper.SetEnvPrefix(EnvPrefix)
	viper.SetConfigType(ConfigTag)

	viper.AddConfigPath(confPath)
	viper.AddConfigPath("./conf")
	viper.AddConfigPath(fmt.Sprintf("%s/pipeline", confPath))
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		viper.AddConfigPath("../conf")
		viper.AddConfigPath("../../conf")
	}

	viper.SetConfigName(ConfigName)
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Errorf("Load config file %s failed：%s", ConfigName, err.Error()))
	}

	if PipeineConfig == nil {
		PipeineConfig = new(Config)
	}
	if err := viper.Unmarshal(PipeineConfig, func(dc *mapstructure.DecoderConfig) {
		dc.TagName = ConfigTag
	}); err != nil {
		panic(fmt.Errorf("Unmarshal config file %s failed：%s", ConfigName, err.Error()))
	}

	zlog.SugarInfof("Loaded config from %s", confPath)
	PipeineConfig.Display()
	return PipeineConfig, nil
}

type Config struct {
	MLOPS    conf.MLOPS
	Pipeline Pipeine
}

func (c *Config) Display() {
	bs, _ := json.MarshalIndent(c, "", "    ")
	zlog.SugarInfoln(string(bs))
}

type Pipeine struct {
	Cvat  Cvat
	Redis Redis
}

type Cvat struct {
	Host string
	Port string
}

type Redis struct {
	Addrs      string `json:"addrs" yaml:"addrs"`
	Database   int    `json:"database" yaml:"database"`
	Username   string `json:"username" yaml:"username"`
	Password   string `json:"password" yaml:"password"`
	Timeout    int    `json:"timeout" yaml:"timeout"`
	MasterName string `json:"masterName" yaml:"masterName"`
}
