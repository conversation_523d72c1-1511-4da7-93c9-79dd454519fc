package util

import (
	"encoding/json"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"

	"k8s.io/client-go/discovery"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/mlops/pipeline/internal/core/consts"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var currentNamespace = sync.OnceValues(func() (string, error) {
	if e := os.Getenv("CURRENT_NAMESPACE"); e != "" {
		return e, nil
	}
	if e := os.Getenv("POD_NAMESPACE"); e != "" {
		return e, nil
	}
	nsFile := "/run/secrets/kubernetes.io/serviceaccount/namespace"
	ns, err := os.ReadFile(nsFile)
	if err != nil {
		zlog.SugarErrorf("Get ns from file %s failed: %v", nsFile, err)
		return "", err
	}
	return strings.TrimSuffix(string(ns), "\n"), err
})

var currentPodName = sync.OnceValues(func() (string, error) {
	if name := os.Getenv("POD_NAME"); name != "" {
		return name, nil
	} else if name, err := os.Hostname(); err == nil && name != "" {
		return name, nil
	}
	return "", nil
})

func GetKubeConfig() (*rest.Config, error) {
	// os.Setenv("LOCAL_DEBUG_MODEL", "TRUE")
	// os.Setenv("MLOPS_CONF_DIR", "/home/<USER>/workspace/sophon/go/transwarp.io/aip/mlops-pipeline/conf")
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath := filepath.Join(os.Getenv("MLOPS_CONF_DIR"), "kubeconfig")
		return clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	}
	return rest.InClusterConfig()
}

func GetCurrentNamespace() string {
	ns, err := currentNamespace()
	if err != nil {
		zlog.SugarErrorf("Get current namespace failed: %v", err)
		// return default value
		return "llmops"
	}
	return ns
}

func GetCurrentPodName() string {
	name, err := currentPodName()
	if err != nil {
		zlog.SugarErrorf("Get current pod name failed: %v", err)
		return "pipeline"
	}
	return name
}

func InferGVRFromGVK(gvk schema.GroupVersionKind) schema.GroupVersionResource {
	resource := ""
	switch gvk.Kind {
	case "Pod":
		resource = "pods"
	case "Deployment":
		resource = "deployments"
	case "ReplicaSet":
		resource = "replicasets"
	case "StatefulSet":
		resource = "statefulsets"
	case "DaemonSet":
		resource = "daemonsets"
	case "Job":
		resource = "jobs"
	case "CronJob":
		resource = "cronjobs"
	case "Workload":
		resource = "workloads"
	case "JobSet":
		resource = "jobsets"
	case "LeaderWorkerSet":
		resource = "leaderworkersets"
	case "Workflow":
		resource = "workflows"
	case "CronWorkflow":
		resource = "cronworkflows"
	case "RayJob":
		resource = "rayjobs"
	case "RayCluster":
		resource = "rayclusters"
	case "AppWrapper":
		resource = "appwrappers"
	case "EventBus":
		resource = "eventbus"
	default:
		resource = strings.ToLower(gvk.Kind) + "s"
		zlog.SugarWarnf("Warning: Using heuristic for resource name %s for Kind '%s'. Actual resource name might differ.", resource, gvk.Kind)
	}

	return gvk.GroupVersion().WithResource(resource)
}

func CustomLabels() map[string]string {
	labels := map[string]string{
		consts.LabelLLMOpsProductKey:   consts.LabelLLMOpsProductValue,
		consts.LabelLLMOpsComponentKey: consts.LabelLLMOpsComponentValue,
		consts.LabelLLMOpsManagedByKey: k8s.CurrentNamespaceInCluster(),
		consts.LabelTaskManagedByKey:   k8s.CurrentNamespaceInCluster(),
	}
	return labels
}

func HasLLMOpsManagedLabel(labels map[string]string) bool {
	if v, ok := labels[consts.LabelLLMOpsManagedByKey]; ok && v == GetCurrentNamespace() {
		return true
	}

	return false
}

func SupportAPIResources(discoveryClient *discovery.DiscoveryClient, groupVersion string, kind string) bool {
	apiResources, err := discoveryClient.ServerResourcesForGroupVersion(groupVersion)
	if err != nil {
		zlog.SugarWarnf("the server could not find the requested resource, groupVersion: %s kind: %s", groupVersion, kind)
		return false
	}

	for _, res := range apiResources.APIResources {
		if res.Kind == kind {
			return true
		}
	}
	return false
}

func CleanupRuntimeFieldsFromRuntimeObject(obj runtime.Object) (*unstructured.Unstructured, error) {
	var unstructuredObj *unstructured.Unstructured
	if u, ok := obj.(*unstructured.Unstructured); ok {
		unstructuredObj = u
	} else {
		data, err := json.Marshal(obj)
		if err != nil {
			zlog.SugarErrorf("Failed to marshal object to JSON: %v", err)
			return nil, err
		}
		unstructuredObj = &unstructured.Unstructured{}
		if err := json.Unmarshal(data, unstructuredObj); err != nil {
			zlog.SugarErrorf("Failed to unmarshal object to Unstructured: %v", err)
			return nil, err
		}
	}

	return CleanupRuntimeFields(unstructuredObj), nil
}

func CleanupRuntimeFields(obj *unstructured.Unstructured) *unstructured.Unstructured {
	// Deep copy the object to avoid modifying the original
	cleanObj := obj.DeepCopy()

	// 清理 metadata
	// Common fields to remove:
	// - uid, resourceVersion, generation: 这些是Kubernetes内部版本控制信息。
	// - creationTimestamp: 对象创建时间。
	// - managedFields: 记录每个字段由哪个控制器管理，`apply`时不需要。
	// - ownerReferences: 如果是Job/Deployment创建的Pod，此字段记录所有者，`apply`时不需要。
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "uid")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "resourceVersion")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "creationTimestamp")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "generation")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "managedFields")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "annotations", "kubectl.kubernetes.io/last-applied-configuration")

	// 清理 status
	// status字段是对象当前状态的反映，不应保存在YAML文件中用于`apply`。
	unstructured.RemoveNestedField(cleanObj.Object, "status")

	return cleanObj
}

// CleanK8sRuntimeInfo removes Kubernetes runtime information from an object
// to create a clean spec-only version suitable for YAML export.
// This function removes server-generated fields like resourceVersion, uid,
// generation, managedFields, status, etc.
func CleanK8sRuntimeInfo(obj runtime.Object) runtime.Object {
	// Deep copy the object to avoid modifying the original
	cleanObj := obj.DeepCopyObject()

	// Get the meta object to clean metadata
	if metaObj, ok := cleanObj.(metav1.Object); ok {
		// Create clean ObjectMeta keeping only essential fields
		cleanMeta := metav1.ObjectMeta{
			Name:        metaObj.GetName(),
			Namespace:   metaObj.GetNamespace(),
			Labels:      copyStringMap(metaObj.GetLabels()),
			Annotations: copyStringMap(metaObj.GetAnnotations()),
		}

		// Set GenerateName if Name is empty (for template objects)
		if cleanMeta.Name == "" && metaObj.GetGenerateName() != "" {
			cleanMeta.GenerateName = metaObj.GetGenerateName()
		}

		// Set the cleaned metadata
		metaObj.SetName(cleanMeta.Name)
		metaObj.SetNamespace(cleanMeta.Namespace)
		metaObj.SetLabels(cleanMeta.Labels)
		metaObj.SetAnnotations(cleanMeta.Annotations)
		metaObj.SetGenerateName(cleanMeta.GenerateName)

		// Clear runtime-generated fields
		metaObj.SetResourceVersion("")
		metaObj.SetUID("")
		metaObj.SetGeneration(0)
		metaObj.SetSelfLink("")
		metaObj.SetCreationTimestamp(metav1.Time{})
		metaObj.SetDeletionTimestamp(nil)
		metaObj.SetDeletionGracePeriodSeconds(nil)
		metaObj.SetOwnerReferences(nil)
		metaObj.SetFinalizers(nil)
		metaObj.SetManagedFields(nil)
	}

	// Clear status field using reflection for different CRD types
	cleanStatus(cleanObj)

	return cleanObj
}

// copyStringMap creates a deep copy of a string map
func copyStringMap(original map[string]string) map[string]string {
	if original == nil {
		return nil
	}
	copy := make(map[string]string, len(original))
	for k, v := range original {
		copy[k] = v
	}
	return copy
}

// cleanStatus removes the status field from Kubernetes objects using reflection
func cleanStatus(obj runtime.Object) {
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr {
		objValue = objValue.Elem()
	}

	if objValue.Kind() != reflect.Struct {
		return
	}

	// Look for Status field and set it to zero value
	statusField := objValue.FieldByName("Status")
	if statusField.IsValid() && statusField.CanSet() {
		statusField.Set(reflect.Zero(statusField.Type()))
	}
}
