package model

import (
	"context"
	"strings"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/google/uuid"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/util"
)

const (
	Platform               = "platform"
	Sophon                 = "sophon"
	MetaNodeId             = "node_id"
	MetaNodeName           = "node_name"
	MetaOwner              = "owner"
	MetaGPUUseConf         = "gpu_use_conf"
	MetaTemplateType       = "template_type"
	MetaTemplateSourceId   = "template_source_id"
	MetaTemplateSourceName = "template_source_name"
	MetaTemplateSourceInfo = "template_source_info"
	MetaTaskFlow           = "task_flow"
	// TODO 通用配置
	PipelineServiceAccount = "llmops-admin"
	// PipelineServiceAccount = "pipeline-runner"
	LabelKeyCacheEnabled = "pipelines.kubeflow.org/cache_enabled"
	EnvGatewayUrl        = "GATEWAY_URL"
	StsPvcMountPath      = "/data/nfs/"
	StsVolumeName        = "sfs-volume"
	PodInfoVolumeName    = "pod-info"
	ArtifactsVolumeName  = "artifacts"
	AnnotationsPath      = "/etc/podinfo"
	ArtifactsPath        = "/artifact"

	// 在任务 pod 中注入环境变量, 用于给任务中动态创建的 k8s 资源设置 ownerReferences 关联.
	EnvPodUID  = "POD_UID"  // pod uid
	EnvPodName = "POD_NAME" // pod 名称
)

type TaskFlow struct {
	Nodes        []*Node
	Dependencies []*pb.Dependency
	HostNetwork  bool
}

// isV2 任意 UnifyResource 不为空则执行 V2
func (taskFlow *TaskFlow) isV2() bool {
	if taskFlow != nil {
		for _, node := range taskFlow.Nodes {
			if node != nil && node.UnifyResource != nil {
				return true
			}
		}
	}
	return false
}

// isV2 任意 UnifyResource 不为空则执行 V2
func (taskFlow *TaskFlow) toV2() *TaskFlow {
	if taskFlow != nil {
		for index := range taskFlow.Nodes {
			taskFlow.Nodes[index] = taskFlow.Nodes[index].toV2()
		}
	}
	return taskFlow
}

func (taskFlow *TaskFlow) ToWorkflow(ctx context.Context, name, proid string, labels map[string]string, annotations map[string]string) (*v1alpha1.Workflow, error) {
	if name == "" {
		name = "run-" + uuid.New().String()
	}
	if !strings.HasPrefix(name, "run-") {
		name = "run-" + name
	}
	return taskFlow.toV2().toWorkflowV2(ctx, name, proid, labels, annotations)
}

func (taskFlow *TaskFlow) FromWorkflow(workflow *v1alpha1.Workflow) (*TaskFlow, error) {
	isSophon := workflow.Annotations[Platform] == Sophon
	if isSophon {
		util.FromJson(workflow.Annotations[MetaTaskFlow], taskFlow)
		return taskFlow, nil
	}

	taskFlow.HostNetwork = *workflow.Spec.HostNetwork
	taskFlow.Dependencies = []*pb.Dependency{}
	taskFlow.Nodes = []*Node{}

	var dag *v1alpha1.DAGTemplate
	artifactRelation := map[string]map[string]string{}
	for _, template := range workflow.Spec.Templates {
		if template.DAG != nil {
			dag = template.DAG
		}
	}
	for _, task := range dag.Tasks {
		taskFlow.Dependencies = append(taskFlow.Dependencies, &pb.Dependency{
			NodeId:    task.Name,
			DepNodeId: task.Dependencies,
		})
		for _, artifact := range task.Arguments.Artifacts {
			artifactRelation[task.Name][artifact.Name] = artifact.From
		}
	}

	for _, template := range workflow.Spec.Templates {
		if template.DAG != nil {
			continue
		}
		var inputs []*pb.ArtifactConfig
		var outputs []*pb.ArtifactConfig

		params := map[string]string{}
		for _, artifact := range template.Inputs.Artifacts {
			var nodeId string
			var artifactName string

			ss := strings.Split(artifactRelation[template.Name][artifact.Name], ".")
			nodeId = ss[1]
			artifactName = ss[4][:len(ss[4])-2]
			from := &pb.From{
				NodeId:       nodeId,
				ArtifactName: artifactName,
			}
			input := &pb.ArtifactConfig{
				Name: artifact.Name,
				Path: artifact.Path,
				From: from,
			}
			inputs = append(inputs, input)
		}
		for _, artifact := range template.Outputs.Artifacts {
			output := &pb.ArtifactConfig{
				Name: artifact.Name,
				Path: artifact.Path,
			}
			outputs = append(outputs, output)
		}

		for _, parameter := range template.Inputs.Parameters {
			if isSophon {
				params[parameter.Name] = string(*parameter.Value)
			}
		}
		env := map[string]string{}

		for _, e := range template.Container.Env {
			env[e.Name] = e.Value
		}
		node := &Node{
			Id: template.Metadata.Annotations[MetaNodeId],
			TemplateSource: &pb.TemplateSource{
				TemplateType: pb.TaskSourceType(pb.TaskSourceType_value[template.Metadata.Annotations[MetaTemplateType]]),
				SourceId:     template.Metadata.Annotations[MetaTemplateSourceId],
				SourceName:   template.Metadata.Annotations[MetaTemplateSourceName],
				SourceInfo:   template.Metadata.Annotations[MetaTemplateSourceInfo],
			},
			Inputs:     inputs,
			Outputs:    outputs,
			Params:     params,
			Name:       template.Metadata.Annotations[MetaNodeName],
			Container:  (&Container{}).FromK8s(template.Container, template.Metadata.Annotations[MetaGPUUseConf]),
			VolumeCfgs: &VolumeCfgs{},
		}
		node.VolumeCfgs.FromK8s(template.Volumes)
		taskFlow.Nodes = append(taskFlow.Nodes, node)
	}
	return taskFlow, nil
}

func (taskFlow *TaskFlow) FromPb(pb *pb.TaskFlow) *TaskFlow {
	taskFlow.HostNetwork = pb.HostNetwork
	taskFlow.Dependencies = pb.Dependencies
	taskFlow.Nodes = []*Node{}
	for _, node := range pb.Nodes {
		taskFlow.Nodes = append(taskFlow.Nodes, (&Node{}).FromPb(node))
	}
	return taskFlow
}

func (taskFlow *TaskFlow) ToPb() *pb.TaskFlow {
	nodes := make([]*pb.Node, 0)
	for _, node := range taskFlow.Nodes {
		nodes = append(nodes, node.ToPb())
	}

	return &pb.TaskFlow{
		HostNetwork:  taskFlow.HostNetwork,
		Dependencies: taskFlow.Dependencies,
		Nodes:        nodes,
	}
}

func (taskFlow *TaskFlow) FromPbV2(pb *pb.TaskFlowV2) *TaskFlow {
	taskFlow.HostNetwork = pb.GetHostNetwork()
	taskFlow.Dependencies = pb.GetDependencies()
	taskFlow.Nodes = make([]*Node, 0, len(pb.GetNodes()))
	for _, node := range pb.GetNodes() {
		taskFlow.Nodes = append(taskFlow.Nodes, (&Node{}).FromPbV2(node))
	}
	return taskFlow
}

func (taskFlow *TaskFlow) ToPbV2() *pb.TaskFlowV2 {
	nodes := make([]*pb.NodeV2, 0, len(taskFlow.Nodes))
	for _, node := range taskFlow.Nodes {
		nodes = append(nodes, node.ToPbV2())
	}
	return &pb.TaskFlowV2{
		HostNetwork:  taskFlow.HostNetwork,
		Dependencies: taskFlow.Dependencies,
		Nodes:        nodes,
	}
}
