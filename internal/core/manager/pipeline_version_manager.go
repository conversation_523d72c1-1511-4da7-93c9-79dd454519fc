package core

import (
	"context"
	"fmt"
	"strings"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/util/retry"
	_const "transwarp.io/mlops/pipeline/const"

	eventsv1alpha1 "github.com/argoproj/argo-events/pkg/apis/events/v1alpha1"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/pipeline/internal/core/model"
	"transwarp.io/mlops/pipeline/internal/dao"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type IPipelineVersionManager interface {
	CreatePipelineVersion(ctx context.Context, pipelineVersionReq *model.PipelineVersion) (string, error)
	UpdatePipelineVersion(ctx context.Context, pipelineVersionReq *model.PipelineVersion) (string, error)
	GetPipelineVersion(ctx context.Context, id string) (*model.PipelineVersion, error)
	DeletePipelineVersion(ctx context.Context, id string) error
	DeletePipelineVersions(ctx context.Context, ids []string) error
	ListPipelineVersions(ctx context.Context, pipelineIds ...string) ([]*pb.PipelineVersion, error)
	StartPipelineVersion(ctx context.Context, id string) (string, error)
	StopPipelineVersion(ctx context.Context, id string) error
	OncePipelineVersion(ctx context.Context, id string) (string, error)
	ExportPipelineVersionYaml(ctx context.Context, id string) (*pb.ExportPipelineVersionRsp, error)
	CreatePipelineVersionByYaml(ctx context.Context, req *pb.CreatePipelineVersionByYamlReq) (string, error)
	CheckPipelineVersionNameUnique(ctx context.Context, name string, pipelineId string) error
}

type PipelineVersionManager struct{}

func NewPipelineVersionManager() *PipelineVersionManager {
	return &PipelineVersionManager{}
}

func (m *PipelineVersionManager) CheckPipelineVersionNameUnique(ctx context.Context, name string, pipelineId string) error {
	count, err := dao.PipelineVersionDAOInst.CountByNameAndPipelineId(name, pipelineId)
	if err != nil {
		return err
	}
	if count > 0 {
		return stderr.PipelineVersionNameExist.Error()
	}
	return nil
}

func (m *PipelineVersionManager) GetPipelineVersion(ctx context.Context, id string) (*model.PipelineVersion, error) {
	entity, err := dao.PipelineVersionDAOInst.GetById(id)
	if err != nil {
		return nil, err
	}
	pipeline, err := dao.PipelineDAOInst.GetById(entity.PipelineID)
	if err != nil {
		return nil, err
	}
	pipelineVersion := (&model.PipelineVersion{}).FromModel(entity)

	err = m.completePipelineVersionWithState(ctx, pipelineVersion, pipeline.ProjectID)
	return pipelineVersion, err
}

func (m *PipelineVersionManager) completePipelineVersionWithState(ctx context.Context, version *model.PipelineVersion, projectId string) error {
	project, err := client.MustGetCASHTTPClient().GetProjectDetailById(ctx, projectId)
	if err != nil {
		return err
	}
	switch version.SchedulingStyle {
	case pb.PipelineVersion_IMMEDIATE:
		return nil
	case pb.PipelineVersion_TIMING:
		if version.TimingConfig == nil {
			return stderr.Internal.Error("timing config is nil")
		}
		_, err = client.MustGetArgoWorkflowClient().CronWorkflowLister.CronWorkflows(project.TenantUID).Get(model.GenName(version.Id))
		if err != nil && !errors.IsNotFound(err) {
			return err
		}
		if !errors.IsNotFound(err) {
			version.TimingConfig.Status = pb.TimingConfig_Enable
		}

	case pb.PipelineVersion_EVENT:
		if version.EventConfig == nil {
			return stderr.Internal.Error("event config is nil")
		}
		version.EventConfig.Status = pb.EventConfig_Disable
		_, err = client.MustGetArgoEventClient().SensorLister.Sensors(project.TenantUID).Get(model.GenName(version.Id))
		if err != nil && !errors.IsNotFound(err) {
			return err
		}
		if !errors.IsNotFound(err) {
			version.EventConfig.Status = pb.EventConfig_Enable
		}
	}

	return nil
}

func (m *PipelineVersionManager) completePipelineVersionsWithState(ctx context.Context, versions []*pb.PipelineVersion, projectId string) error {
	project, err := client.MustGetCASHTTPClient().GetProjectDetailById(ctx, projectId)
	if err != nil {
		return err
	}
	ids, err := m.ListOnlinePipelineVersionId(project.TenantUID)
	if err != nil {
		return err
	}
	for _, version := range versions {
		switch version.Style {
		case pb.PipelineVersion_IMMEDIATE:
			continue
		case pb.PipelineVersion_TIMING:
			if version.TimingConfig == nil {
				continue
			}
			if _, ok := ids[version.Id]; ok {
				version.TimingConfig.Status = pb.TimingConfig_Enable
			}
		case pb.PipelineVersion_EVENT:
			if version.EventConfig == nil {
				continue
			}
			if _, ok := ids[version.Id]; ok {
				version.EventConfig.Status = pb.EventConfig_Enable
			}
		}
	}

	return nil
}

func (m *PipelineVersionManager) ListOnlinePipelineVersionId(tenantUid string) (map[string]interface{}, error) {
	ids := make(map[string]interface{})
	cronWorkflows, err := client.MustGetArgoWorkflowClient().CronWorkflowLister.CronWorkflows(tenantUid).List(labels.Everything())
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	for _, cronWorkflow := range cronWorkflows {
		if !cronWorkflow.Spec.Suspend {
			ids[strings.TrimPrefix(cronWorkflow.Name, _const.PrefixName)] = struct{}{}
		}
	}
	sensors, err := client.MustGetArgoEventClient().SensorLister.Sensors(tenantUid).List(labels.Everything())
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	for _, sensor := range sensors {
		ids[strings.TrimPrefix(sensor.Name, _const.PrefixName)] = struct{}{}
	}
	return ids, nil
}

func (m *PipelineVersionManager) CreatePipelineVersion(ctx context.Context, pipelineVersion *model.PipelineVersion) (string, error) {
	size, err := dao.PipelineVersionDAOInst.CountByNameAndPipelineId(pipelineVersion.Name, pipelineVersion.PipelineId)
	if err != nil {
		return "", err
	}
	if size > 0 {
		return "", stderr.PipelineVersionNameExist.Error()
	}
	_, err = PipelineMgr.GetPipeline(ctx, pipelineVersion.PipelineId)
	if err != nil {
		return "", err
	}

	pipelineVersionId, err := dao.PipelineVersionDAOInst.Create(pipelineVersion.ToModel())
	if err != nil {
		return "", err
	}
	return pipelineVersionId, nil
}

func (m *PipelineVersionManager) UpdatePipelineVersion(ctx context.Context, pipelineVersion *model.PipelineVersion) (string, error) {
	_, err := dao.PipelineVersionDAOInst.GetById(pipelineVersion.Id)
	if err != nil {
		return "", err
	}
	_, err = PipelineMgr.GetPipeline(ctx, pipelineVersion.PipelineId)
	if err != nil {
		return "", err
	}

	err = m.StopPipelineVersion(ctx, pipelineVersion.Id)
	if err != nil {
		return "", err
	}

	id, err := dao.PipelineVersionDAOInst.Save(pipelineVersion.ToModel())
	if pipelineVersion.SchedulingStyle != pb.PipelineVersion_IMMEDIATE {
		_, err = m.StartPipelineVersion(ctx, pipelineVersion.Id)
		if err != nil {
			return "", err
		}
	}
	if err != nil {
		return "", err
	}
	return id, nil
}

func (m *PipelineVersionManager) ListPipelineVersions(ctx context.Context, pipelineIds ...string) ([]*pb.PipelineVersion, error) {
	entities, err := dao.PipelineVersionDAOInst.List(pipelineIds...)
	if err != nil {
		return nil, err
	}

	versions := make([]*pb.PipelineVersion, 0)
	for _, entry := range entities {
		version := (&model.PipelineVersion{}).FromModel(entry)
		versions = append(versions, version.ToPb())
	}
	if len(versions) > 0 {
		pipeline, err := dao.PipelineDAOInst.GetById(versions[0].PipelineId)
		if err != nil {
			return nil, err
		}
		err = m.completePipelineVersionsWithState(ctx, versions, pipeline.ProjectID)
	}
	if err != nil {
		return nil, err
	}
	return versions, nil
}

func (m *PipelineVersionManager) DeletePipelineVersion(ctx context.Context, id string) error {
	_, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return err
	}
	runs, _, err := RunMgr.ListRun(ctx, "", &pb.ListRunsReq_Filter{
		SourceId: []string{id},
	}, &pb.PageReq{})
	if err != nil {
		return fmt.Errorf("list pipeline version runs: %w", err)
	}
	// 运行中的不能删除
	for _, run := range runs {
		if run.Status == pb.Run_Running {
			return stderr.DeleteRunningPipelineTask.Error()
		}
	}
	// 删除 job
	err = PipelineVersionMgr.StopPipelineVersion(ctx, id)
	if err != nil {
		return err
	}
	// 删除 runs
	//for _, run := range runs {
	//	err = RunMgr.DeleteRun(ctx, run.Id)
	//	if err != nil {
	//		return err
	//	}
	//}
	return dao.PipelineVersionDAOInst.Delete(id)
}

func (m *PipelineVersionManager) DeletePipelineVersions(ctx context.Context, ids []string) error {
	for _, id := range ids {
		_, err := dao.PipelineVersionDAOInst.GetById(id)
		if err != nil {
			return err
		}
	}
	runs, _, err := RunMgr.ListRun(ctx, "", &pb.ListRunsReq_Filter{
		SourceId: ids,
	}, &pb.PageReq{})
	if err != nil {
		return fmt.Errorf("list pipeline version runs: %w", err)
	}
	// 运行中的不能删除
	for _, run := range runs {
		if run.Status == pb.Run_Running {
			return stderr.DeleteRunningPipelineTask.Error()
		}
	}
	// 删除 job
	for _, id := range ids {
		err = PipelineVersionMgr.StopPipelineVersion(ctx, id)
		if err != nil {
			return err
		}
	}
	// 删除 runs
	//for _, run := range runs {
	//	err = RunMgr.DeleteRun(ctx, run.Id)
	//	if err != nil {
	//		return err
	//	}
	//}
	return dao.PipelineVersionDAOInst.DeleteByIds(ids)
}

func (m *PipelineVersionManager) CreatePipelineVersionByYaml(ctx context.Context, req *pb.CreatePipelineVersionByYamlReq) (string, error) {
	pipelineVersion := &model.PipelineVersion{}
	pipelineVersion, err := pipelineVersion.FromYaml([]byte(req.CreatePipelineVersionByYamlBody.Yaml))
	if err != nil {
		return "", err
	}
	pipelineVersion.Name = req.CreatePipelineVersionByYamlBody.Name
	pipelineVersion.Desc = req.CreatePipelineVersionByYamlBody.Desc
	pipelineVersion.PipelineId = req.CreatePipelineVersionByYamlBody.PipelineId
	return m.CreatePipelineVersion(ctx, pipelineVersion)
}

func (m *PipelineVersionManager) ExportPipelineVersionYaml(ctx context.Context, id string) (*pb.ExportPipelineVersionRsp, error) {
	pipelineVersion, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return nil, err
	}
	pipeline, err := PipelineMgr.GetPipeline(ctx, pipelineVersion.PipelineId)
	if err != nil {
		return nil, err
	}
	project, err := client.MustGetCASHTTPClient().GetProjectDetailById(ctx, pipeline.ProjectId)
	if err != nil {
		return nil, err
	}
	yaml, err := pipelineVersion.ToYaml(ctx, pipeline, project.TenantUID)
	if err != nil {
		return nil, err
	}
	return &pb.ExportPipelineVersionRsp{
		PipelineVersionFile: yaml,
		FileName:            pipelineVersion.Name,
	}, nil
}

func (m *PipelineVersionManager) OncePipelineVersion(ctx context.Context, id string) (string, error) {
	version, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return "", err
	}
	pipeline, err := PipelineMgr.GetPipeline(ctx, version.PipelineId)
	if err != nil {
		return "", err
	}
	run := &model.Run{
		ProjectId: pipeline.ProjectId,
		TaskSource: &pb.TaskSource{
			SourceType: pb.TaskSourceType_PIPELINE,
			SourceId:   version.Id,
			SourceName: version.Name,
			SourceInfo: "",
		},
		TaskFlow: version.TaskFlow,
	}
	runId, err := RunMgr.SubmitRun(ctx, run)
	if err != nil {
		return "", err
	}
	return runId, nil
}

func isImmediate(pipelineVersion *model.PipelineVersion) bool {
	return pipelineVersion.SchedulingStyle == pb.PipelineVersion_IMMEDIATE ||
		(pipelineVersion.TimingConfig == nil && pipelineVersion.EventConfig == nil)
}

func (m *PipelineVersionManager) StartPipelineVersion(ctx context.Context, id string) (string, error) {
	version, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return "", err
	}
	pipeline, err := PipelineMgr.GetPipeline(ctx, version.PipelineId)
	if err != nil {
		return "", err
	}
	if isImmediate(version) {
		return "", stderr.StartImmediatePipelineVersion.Error()
	}
	expectResources, err := version.ToRuntimeResources(ctx, pipeline)
	if err != nil {
		return "", err
	}
	err = SyncRuntimeResources(pipeline, version, expectResources)
	if err != nil {
		return "", err
	}

	return version.Id, err
}

func SyncRuntimeResources(pipeline *model.Pipeline, version *model.PipelineVersion, expectResources *model.RuntimeResources) error {
	var err error
	err = syncSensor(pipeline, version, expectResources.Sensors)
	if err != nil {
		return err
	}
	err = syncCronWorkflows(pipeline, version, expectResources.CronWorkflows)
	if err != nil {
		return err
	}
	return nil
}

func syncSensor(pipeline *model.Pipeline, version *model.PipelineVersion, expects map[string]*eventsv1alpha1.Sensor) error {
	selector := labels.SelectorFromSet(version.ToLabels(pipeline.ProjectId))

	// 1. 获取当前 Sensor 列表
	curs, err := client.MustGetArgoEventClient().SensorLister.List(selector)
	if err != nil {
		return stderr.Internal.Errorf("failed to list Sensors: %+v", err)
	}

	client := client.MustGetArgoEventClient().ClientSet.ArgoprojV1alpha1().Sensors

	// 2. 遍历已有资源：更新或删除
	for _, cur := range curs {
		if expect, ok := expects[cur.Name]; ok {
			err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
				latest, err := client(cur.Namespace).Get(context.Background(), cur.Name, metav1.GetOptions{})
				if err != nil {
					return err
				}
				updated := latest.DeepCopy()
				updated.Spec = expect.Spec
				updated.Labels = expect.Labels
				updated.Annotations = expect.Annotations

				_, err = client(updated.Namespace).Update(context.Background(), updated, metav1.UpdateOptions{})
				return err
			})
			if err != nil {
				return stderr.Internal.Errorf("failed to update Sensor %s: %+v", cur.Name, err)
			}
			delete(expects, cur.Name)
		} else {
			err = client(cur.Namespace).Delete(context.Background(), cur.Name, metav1.DeleteOptions{})
			if err != nil {
				return stderr.Internal.Errorf("failed to delete Sensor %s: %+v", cur.Name, err)
			}
		}
	}

	// 3. 创建剩余期望资源
	for _, expect := range expects {
		_, err := client(expect.Namespace).Create(context.Background(), expect, metav1.CreateOptions{})
		if err != nil && !errors.IsAlreadyExists(err) {
			return stderr.Internal.Errorf("failed to create Sensor %s: %+v", expect.Name, err)
		}
	}

	return nil
}

func syncCronWorkflows(pipeline *model.Pipeline, version *model.PipelineVersion, expects map[string]*v1alpha1.CronWorkflow) error {
	selector := labels.SelectorFromSet(version.ToLabels(pipeline.ProjectId))

	// 1. 获取当前 CronWorkflows 列表
	curs, err := client.MustGetArgoWorkflowClient().CronWorkflowLister.List(selector)
	if err != nil {
		return stderr.Internal.Errorf("failed to list Sensors: %+v", err)
	}

	cWClient := client.MustGetArgoWorkflowClient().ClientSet.ArgoprojV1alpha1().CronWorkflows

	// 2. 遍历已有资源：更新或删除
	for _, cur := range curs {
		if expect, ok := expects[cur.Name]; ok {
			err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
				latest, err := cWClient(cur.Namespace).Get(context.Background(), cur.Name, metav1.GetOptions{})
				if err != nil {
					return err
				}
				updated := latest.DeepCopy()
				updated.Spec = expect.Spec
				updated.Labels = expect.Labels
				updated.Annotations = expect.Annotations

				_, err = cWClient(updated.Namespace).Update(context.Background(), updated, metav1.UpdateOptions{})
				return err
			})
			if err != nil {
				return stderr.Internal.Errorf("failed to update cron workflow %s: %+v", cur.Name, err)
			}
			delete(expects, cur.Name)
		} else {
			// 存在workflow还在运行中的情况不可删除
			workflows, err := client.MustGetArgoWorkflowClient().WorkflowLister.List(selector)
			if err != nil {
				return stderr.Internal.Errorf("failed to list Sensors: %+v", err)
			}

			isAllCompleted := true
			for _, workflow := range workflows {
				if !workflow.Status.Phase.Completed() {
					isAllCompleted = false
					err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
						latest, err := cWClient(cur.Namespace).Get(context.Background(), cur.Name, metav1.GetOptions{})
						if err != nil {
							return err
						}
						updated := latest.DeepCopy()
						updated.Spec.Suspend = true
						_, err = cWClient(updated.Namespace).Update(context.Background(), updated, metav1.UpdateOptions{})
						return err
					})
					if err != nil {
						return stderr.Internal.Errorf("failed to update cron workflow %s: %+v", cur.Name, err)
					}
				}
			}
			if isAllCompleted {
				err = cWClient(cur.Namespace).Delete(context.Background(), cur.Name, metav1.DeleteOptions{})
				if err != nil {
					return stderr.Internal.Errorf("failed to delete Sensor %s: %+v", cur.Name, err)
				}
			}
		}
	}

	// 3. 创建剩余期望资源
	for _, expect := range expects {
		_, err := cWClient(expect.Namespace).Create(context.Background(), expect, metav1.CreateOptions{})
		if err != nil && !errors.IsAlreadyExists(err) {
			return stderr.Internal.Errorf("failed to create Sensor %s: %+v", expect.Name, err)
		}
	}

	return nil
}

func (m *PipelineVersionManager) StopPipelineVersion(ctx context.Context, id string) error {
	version, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return err
	}
	pipeline, err := PipelineMgr.GetPipeline(ctx, version.PipelineId)
	if err != nil {
		return err
	}
	expectResources := &model.RuntimeResources{}
	err = SyncRuntimeResources(pipeline, version, expectResources)
	if err != nil {
		return err
	}
	return err
}
