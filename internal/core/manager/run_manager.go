package core

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strconv"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"transwarp.io/mlops/mlops-std/util"
	run2 "transwarp.io/mlops/pipeline/internal/core/manager/run"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	common "transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	config "transwarp.io/mlops/pipeline/internal/config"
	"transwarp.io/mlops/pipeline/internal/core/model"
	"transwarp.io/mlops/pipeline/internal/dao"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

// m *RunManager IRunManager

type RunManager struct{}

func NewRunManager() *RunManager {
	return &RunManager{}
}

const (
	KubeflowSecretName          = "mlpipeline-minio-artifact"
	LabelKeyApplicationCrdId    = "application-crd-id"
	LabelValueKubeflowPipelines = "kubeflow-pipelines"
	AccessKey                   = "accesskey"
	SecretKey                   = "secretkey"
)

type IRunManager interface {
	SubmitRun(ctx context.Context, run *model.Run) (string, error)
	RetryRun(ctx context.Context, runId string) error
	TerminateRun(ctx context.Context, runId string) error
	ListRun(ctx context.Context, projectId string, filter *pb.ListRunsReq_Filter, pageConfig *pb.PageReq) ([]*model.Run, int64, error)
	GetRun(ctx context.Context, runId string) (*model.Run, error)
	DeleteRun(ctx context.Context, runId string) error
	GetRunStepLogs(ctx context.Context, runId string, nodeId string) (io.Reader, error)
	GetRunStepArtifact(ctx context.Context, runId string, nodeId string, artifactName string) (io.Reader, string, error)
	GetRunStepEvents(ctx context.Context, id string, step string, tenantId string) ([]*common.PodEvent, error)
	GetEventsByLabels(ctx context.Context, labelsMap map[string]string) ([]*common.PodEvent, error)
	GetRunStepPodInfo(ctx context.Context, id string, step string) ([]byte, error)
}

func (m *RunManager) getRunPodNameAndNamespace(ctx context.Context, id string, step string) (string, string, error) {
	run, err := dao.RunDAOInst.Get(ctx, id)
	if err != nil {
		return "", "", err
	}
	var workflow v1alpha1.Workflow
	util.FromJson(run.WorkflowRuntimeManifest, &workflow)
	for _, node := range workflow.Status.Nodes {
		if node.TemplateName == step {
			return node.ID, workflow.Namespace, nil
		}
	}
	return "", "", stderr.PipelineStepNotFound.Error()
}

func (m *RunManager) GetRunStepLogs(ctx context.Context, runId string, nodeId string) (io.Reader, error) {
	podName, namespace, err := m.getRunPodNameAndNamespace(ctx, runId, nodeId)
	if err != nil {
		return nil, err
	}
	serviceCfg := config.PipeineConfig.MLOPS.ServiceConfig
	var limitLines int64
	if serviceCfg.PodLogLimitLine != "" {
		limitLines, err = strconv.ParseInt(serviceCfg.PodLogLimitLine, 10, 64)
		if err != nil {
			return nil, err
		}
	} else {
		limitLines = int64(200)
	}

	var limitBytes int64
	if serviceCfg.PodLogLimitBytes != "" {
		limitBytes, err = strconv.ParseInt(serviceCfg.PodLogLimitBytes, 10, 64)
		if err != nil {
			return nil, err
		}
	} else {
		limitBytes = int64(1024 * 1024 * 2) // 2MB
	}
	log, err := client.MustGetK8sClient().GetContainerLogs(ctx, namespace, podName, "main", &client.GetLogsOptions{
		TailLines:  limitLines,
		LimitBytes: limitBytes,
	})
	if err != nil && errors.IsNotFound(err) {
		zlog.SugarDebugf("runid %s step %s namespace %s pod %s logs step from s3", runId, nodeId, namespace, podName)
		reader, _, err := m.GetRunStepArtifact(ctx, runId, nodeId, "main-logs")
		return reader, err
	}
	return log, err
}

func (m *RunManager) GetRunStepArtifact(ctx context.Context, runId string, nodeId string, artifactName string) (io.Reader, string, error) {
	run, err := dao.RunDAOInst.Get(ctx, runId)
	if err != nil {
		return nil, "", err
	}
	var workflow v1alpha1.Workflow
	util.FromJson(run.WorkflowRuntimeManifest, &workflow)

	serviceCfg := config.PipeineConfig.MLOPS.ServiceConfig
	var limitBytes int64
	if serviceCfg.PodLogLimitBytes != "" {
		limitBytes, err = strconv.ParseInt(serviceCfg.PodLogLimitBytes, 10, 64)
		if err != nil {
			return nil, "", err
		}
	} else {
		limitBytes = int64(1024 * 1024 * 1) // 1MB
	}

	for _, node := range workflow.Status.Nodes {
		if node.TemplateName == nodeId {
			if node.Outputs == nil {
				continue
			}
			for _, output := range node.Outputs.Artifacts {
				if output.Name == artifactName {

					artifact, err := client.MustGetMinioClient().GetObjectTail("mlpipeline", output.S3.Key, limitBytes)
					if err != nil {
						return nil, "", err
					}
					return artifact, "", nil
				}
			}
		}
	}
	return nil, "", stderr.PipelineStepNotFound.Error()
}

func checkTaskFlow(taskFlow *model.TaskFlow) error {
	if taskFlow.Nodes == nil || len(taskFlow.Nodes) == 0 {
		return stderr.PipelineTaskFlowIsNil.Error()
	}
	return nil
}

func (m *RunManager) SubmitRun(ctx context.Context, run *model.Run) (string, error) {
	err := checkTaskFlow(run.TaskFlow)
	if err != nil {
		return "", err
	}
	project, err := client.MustGetCASHTTPClient().GetProjectDetailById(ctx, run.ProjectId)
	if err != nil {
		return "", err
	}
	err = initKubeflowSecret(project.TenantUID)
	if err != nil {
		return "", err
	}
	run.CreateUser = util.GetUsername(ctx)

	workflow, err := run.ToWorkflow(ctx, project.TenantUID)
	if err != nil {
		return "", err
	}
	workflow, err = client.MustGetArgoWorkflowClient().ClientSet.ArgoprojV1alpha1().Workflows(project.TenantUID).Create(ctx, workflow, metav1.CreateOptions{})
	if err != nil {
		return "", stderr.Internal.Errorf("failed to create workflow, err %+v", err)
	}

	return string(workflow.UID), nil
}

func (m *RunManager) RetryRun(ctx context.Context, runId string) error {
	run, err := dao.RunDAOInst.Get(ctx, runId)
	if err != nil {
		return err
	}
	if run.Conditions == pb.Run_Succeeded.String() || run.Conditions == pb.Run_Running.String() {
		return stderr.CanOnlyRetryFailedOrErrorRun.Error(run.Conditions)
	}
	if run.WorkflowRuntimeManifest == "" {
		return stderr.Internal.Errorf("workflow cannot be retried because WorkflowRuntimeManifest is null")
	}
	var workflow v1alpha1.Workflow
	if err := json.Unmarshal([]byte(run.WorkflowRuntimeManifest), &workflow); err != nil {
		return stderr.Internal.Errorf("Failed to retrieve the runtime pipeline spec from the run, err %+v", err)
	}
	err = client.MustGetArgoWorkflowClient().RetryWorkflow(&workflow)
	if err != nil {
		return err
	}
	newWorkflow := run2.NewWorkflow(&workflow)
	err = dao.RunDAOInst.Update(runId, newWorkflow.Condition(), 0, newWorkflow.ToStringForStore())
	if err != nil {
		return stderr.Internal.Errorf("Failed to update the database entry. err: %+v", err)
	}
	return nil
}

func (m *RunManager) TerminateRun(ctx context.Context, runId string) error {
	run, err := dao.RunDAOInst.Get(ctx, runId)
	if err != nil {
		return err
	}
	return client.MustGetArgoWorkflowClient().TerminateWorkflow(run.Name, run.Namespace)
}

func (m *RunManager) GetRun(ctx context.Context, runId string) (*model.Run, error) {
	entry, err := dao.RunDAOInst.Get(ctx, runId)
	if err != nil {
		return nil, err
	}
	run := &model.Run{}
	run.FromEntry(*entry)
	_, err = dao.MetricDAOInst.ListByRunId(ctx, runId)
	if err != nil {
		return nil, err
	}
	return run, nil
}

func (m *RunManager) DeleteRun(ctx context.Context, runId string) error {
	// 有些情况下 kubelfow 的 runs 会被其他人删除, 所以这里不抛出 err
	err := dao.RunDAOInst.Delete(ctx, runId)
	if err != nil {
		zlog.SugarWarnf("failed to delete run from db: %+v", err)
	}
	return dao.RunDAOInst.Delete(ctx, runId)
}

func (m *RunManager) ListRun(ctx context.Context, projectId string, filter *pb.ListRunsReq_Filter, pageConfig *pb.PageReq) ([]*model.Run, int64, error) {
	entries, size, err := dao.RunDAOInst.List(projectId, filter, pageConfig)
	if err != nil {
		return nil, 0, err
	}
	runs := make([]*model.Run, 0)
	for _, entry := range entries {
		run := &model.Run{}
		run.FromEntry(*entry)
		runs = append(runs, run)
	}
	return runs, size, nil
}

func (m *RunManager) GetRunStepPodInfo(ctx context.Context, id string, step string) ([]byte, error) {
	podName, namespace, err := m.getRunPodNameAndNamespace(ctx, id, step)
	if err != nil {
		return nil, err
	}
	if len(podName) == 0 {
		return nil, stderr.ParamParseFailure.Error(podName)
	}

	if len(namespace) == 0 {
		return nil, stderr.ParamParseFailure.Error(namespace)
	}

	return client.MustGetK8sClient().GetPodInfo(ctx, namespace, podName)
}

func (m *RunManager) GetRunStepEvents(ctx context.Context, id string, step string, tenantId string) ([]*common.PodEvent, error) {
	podName, namespace, err := m.getRunPodNameAndNamespace(ctx, id, step)
	if err != nil {
		zlog.SugarInfof("pod is nil, please check events, runId %s, step %s", id, step)
		namespace = tenantId
	}

	events, err := client.MustGetK8sClient().GetPodEvents(ctx, namespace, id, podName)
	if err != nil {
		return nil, err
	}
	podEvents := make([]*common.PodEvent, 0)
	for _, event := range events {
		podEvents = append(podEvents, &common.PodEvent{
			Type:           event.Type,
			Reason:         event.Reason,
			FirstTimestamp: event.FirstTimestamp.Unix(),
			LastTimestamp:  event.LastTimestamp.Unix(),
			Count:          event.Count,
			Message:        event.Message,
		})
	}
	return podEvents, nil
}

func (m *RunManager) GetEventsByLabels(ctx context.Context, labels map[string]string) ([]*common.PodEvent, error) {
	eventList, err := client.MustGetK8sClient().GetPodEventsByLabels(ctx, "", labels)
	if err != nil {
		return nil, err
	}
	zlog.SugarDebugf("query events list label:%+v,result:%+v", labels, eventList)
	podEvents := make([]*common.PodEvent, 0)
	for _, event := range eventList.Items {
		podEvents = append(podEvents, &common.PodEvent{
			Type:           event.Type,
			Reason:         event.Reason,
			FirstTimestamp: event.FirstTimestamp.Unix(),
			LastTimestamp:  event.LastTimestamp.Unix(),
			Count:          event.Count,
			Message:        event.Message,
		})
	}
	return podEvents, nil
}

func initKubeflowSecret(namespace string) error {
	_, err := client.MustGetK8sClient().SecretLister.Secrets(namespace).Get(KubeflowSecretName)
	if err == nil {
		return nil
	}
	secret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      KubeflowSecretName,
			Namespace: namespace,
			Labels: map[string]string{
				LabelKeyApplicationCrdId: LabelValueKubeflowPipelines,
			},
		},
		Data: map[string][]byte{
			AccessKey: []byte(config.PipeineConfig.MLOPS.Warehouse.Minio.AccessKey),
			SecretKey: []byte(config.PipeineConfig.MLOPS.Warehouse.Minio.SecretKey),
		},
		Type: v1.SecretTypeOpaque,
	}
	_, err = client.MustGetK8sClient().Clientset.CoreV1().Secrets(namespace).Create(context.Background(), secret, metav1.CreateOptions{})
	if err != nil {
		if errors.IsAlreadyExists(err) {
			zlog.SugarInfof("Secret %s already exists\n", secret.Name)
		} else {
			fmt.Printf("Error creating secret: %s\n", err.Error())
			return err
		}
	}
	return nil
}
