package handlerframework

import (
	"context"
	"fmt"

	"transwarp.io/mlops/pipeline/internal/util"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/common"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/dao"
)

type TaskGenerator interface {
	GenerateTask(obj runtime.Object) (*model.Task, error)
	SaveTask(task *model.Task) error
}

type DefaultTaskGenerator struct {
	taskBuilder *common.TaskBuilder
}

func NewDefaultTaskGenerator() *DefaultTaskGenerator {
	dte := &DefaultTaskGenerator{
		taskBuilder: common.NewTaskBuilder(),
	}
	return dte
}

func (dte *DefaultTaskGenerator) GenerateTask(obj runtime.Object) (*model.Task, error) {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return nil, fmt.Errorf("object does not implement metav1.Object")
	}

	gvk := obj.GetObjectKind().GroupVersionKind()
	gvr := util.InferGVRFromGVK(gvk)

	// Use TaskBuilder to create the task
	opts := &common.TaskBuildOptions{
		Type:     model.TaskTypeUnknown,
		Priority: model.TaskPriorityMedium,
		Status:   model.TaskStatusPending,
		GVK:      gvk,
		GVR:      gvr,
	}

	task := dte.taskBuilder.BuildTaskFromObject(metaObj, opts)
	if task == nil {
		return nil, fmt.Errorf("failed to build task from meta object")
	}

	return task, nil
}

func (dtg *DefaultTaskGenerator) SaveTask(task *model.Task) error {
	// Convert core task model to DAO model
	daoTask, err := dtg.taskBuilder.ToDAOModel(task)
	if err != nil {
		return err
	}

	// Save to database
	// _, err = dao.MustGetTaskDAO().Save(context.Background(), daoTask)
	_, err = dao.MustGetTaskDAO().CreateIfNotExists(context.Background(), daoTask)
	if err != nil {
		return err
	}

	return nil
}
