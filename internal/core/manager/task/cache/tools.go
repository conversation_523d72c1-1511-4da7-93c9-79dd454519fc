package cache

import (
	"context"
	"fmt"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	dynamicClient dynamic.Interface
	toolsOnce     sync.Once
)

func mustGetDynamicClient() dynamic.Interface {
	if dynamicClient == nil {
		toolsOnce.Do(func() {
			config, err := util.GetKubeConfig()
			if err != nil {
				panic(err)
			}
			dynamicClient, err = dynamic.NewForConfig(config)
			if err != nil {
				panic(err)
			}
		})
	}
	return dynamicClient
}

func getObjectFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	name := ownerRef.Name
	kind := ownerRef.Kind

	gvk, err := inferGVKFromOwnerRef(ownerRef)
	if err != nil {
		zlog.SugarErrorf("Failed to infer GVk from owner reference: %v", err)
		return nil, err
	}

	obj, err := getObj(namespace, name, gvk)
	if err != nil {
		return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
	}

	return obj, nil
}

func findAllAncestor(obj metav1.Object) ([]metav1.Object, error) {
	ancestors := make([]metav1.Object, 0)
	namespace := obj.GetNamespace()

	for _, ref := range obj.GetOwnerReferences() {
		ancestor, err := findAncestorFromOwnerRef(namespace, ref)
		if err != nil {
			return nil, err
		}
		ancestors = append(ancestors, ancestor)
	}

	return ancestors, nil
}

func findAncestorFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	var ancestor metav1.Object
	for {
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvk, err := inferGVKFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVK from owner reference: %v", err)
			return nil, err
		}

		obj, err := getObj(namespace, name, gvk)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		if obj == nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: not found", kind, namespace, name)
		}
		ancestor = obj

		if len(obj.GetOwnerReferences()) == 0 {
			break
		}
		ownerRef = obj.GetOwnerReferences()[0]
	}

	return ancestor, nil
}

func findAnyAncestor(obj metav1.Object) (metav1.Object, error) {
	ancestor := obj
	namespace := obj.GetNamespace()
	for {
		ownerRefs := ancestor.GetOwnerReferences()
		if len(ownerRefs) == 0 {
			break
		}

		ownerRef := ownerRefs[0]
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvk, err := inferGVKFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVK from owner reference: %v", err)
			return nil, err
		}

		obj, err := getObj(namespace, name, gvk)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		ancestor = obj
	}

	return ancestor, nil
}

func inferGVRFromOwnerRef(ownerRef metav1.OwnerReference) (schema.GroupVersionResource, error) {
	gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
	if err != nil {
		return schema.GroupVersionResource{}, fmt.Errorf("failed to parse APIVersion %s: %w", ownerRef.APIVersion, err)
	}

	gvk := gv.WithKind(ownerRef.Kind)
	return util.InferGVRFromGVK(gvk), nil
}

func inferGVKFromOwnerRef(ownerRef metav1.OwnerReference) (schema.GroupVersionKind, error) {
	gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
	if err != nil {
		return schema.GroupVersionKind{}, fmt.Errorf("failed to parse APIVersion %s: %w", ownerRef.APIVersion, err)
	}

	return gv.WithKind(ownerRef.Kind), nil
}

func inferGVRFromObject(obj metav1.Object) schema.GroupVersionResource {
	gvk := obj.(runtime.Object).GetObjectKind().GroupVersionKind()
	return util.InferGVRFromGVK(gvk)
}

func inferGVKFromObject(obj metav1.Object) schema.GroupVersionKind {
	return obj.(runtime.Object).GetObjectKind().GroupVersionKind()
}

func getObj(namespace, name string, gvk schema.GroupVersionKind) (metav1.Object, error) {
	switch gvk.Kind {
	case "Pod":
		return client.MustGetK8sClient().GetPod(namespace, name)
	case "Deployment":
		return client.MustGetK8sClient().GetDeployment(namespace, name)
	case "ReplicaSet":
		return client.MustGetK8sClient().GetReplicaSet(namespace, name)
	case "StatefulSet":
		return client.MustGetK8sClient().GetStatefulSet(namespace, name)
	case "DaemonSet":
		return client.MustGetK8sClient().GetDaemonSet(namespace, name)
	case "Job":
		return client.MustGetK8sClient().GetJob(namespace, name)
	case "CronJob":
		return client.MustGetK8sClient().GetCronJob(namespace, name)
	case "Workload":
		return client.MustGetKueueClient().GetWorkload(namespace, name)
	case "JobSet":
		return client.MustGetJobSetClient().GetJobSet(namespace, name)
	case "LeaderWorkerSet":
		return client.MustGetLeaderWorkerSetClient().GetLeaderWorkerSet(namespace, name)
	case "Workflow":
		return client.MustGetArgoWorkflowClient().GetWorkflow(namespace, name)
	case "CronWorkflow":
		return client.MustGetArgoWorkflowClient().GetCronWorkflow(namespace, name)
	case "RayJob":
		return client.MustGetRayClient().GetRayJob(namespace, name)
	case "RayCluster":
		return client.MustGetRayClient().GetRayCluster(namespace, name)
	case "AppWrapper":
		return client.MustGetAppWrapperClient().GetAppWrapper(namespace, name)
	case "EventBus":
		return client.MustGetArgoEventClient().GetEventBus(namespace, name)
	default:
		gvr := util.InferGVRFromGVK(gvk)
		obj, err := mustGetDynamicClient().Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", gvr.Resource, namespace, name, err)
		}
		return obj, nil
	}
}
