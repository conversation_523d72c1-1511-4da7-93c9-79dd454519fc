package cache

import (
	"context"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	"transwarp.io/mlops/pipeline/internal/core/manager/task/common"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/dao"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func NewTaskTree(refs []metav1.Object) *TaskTree {
	return &TaskTree{
		root: &TaskRoot{
			refs:      refs,
			workloads: make([]*TaskWorkloadNode, 0),
		},
		mu:          sync.Mutex{},
		taskBuilder: common.NewTaskBuilder(),
	}
}

func (tt *TaskTree) AddTaskWorkloadNode(node *TaskWorkloadNode) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	tt.root.workloads = append(tt.root.workloads, node)

	// Update database task status when tree changes
	tt.updateTaskStatusInDB()
}

func (tt *TaskTree) UpdateTaskWorkloadNode(oldNode, newNode *TaskWorkloadNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for i, node := range tt.root.workloads {
		if node.ref == oldNode.ref {
			if newNode.lastTransitionTime.Before(*node.lastTransitionTime) {
				zlog.SugarWarnf("Skip update workload %s because new transition time %v is before old transition time %v", oldNode.ref.GetName(), newNode.lastTransitionTime, node.lastTransitionTime)
				return nil, true
			}
			for _, child := range node.children {
				newNode.children = append(newNode.children, child)
			}
			tt.root.workloads[i] = newNode

			// Update database task status when tree changes
			tt.updateTaskStatusInDB()
			return nil, true
		}
	}

	return nil, false
}

// try to delete workload node if found in tree
// nil, true meaning successful
// nil, false meaning not found
// err, false meaning throw error when do delete
// err, true no meaning
func (tt *TaskTree) TryDeleteTaskWorkloadNode(node *TaskWorkloadNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for i, twln := range tt.root.workloads {
		if twln.ref.GetUID() == node.ref.GetUID() {
			tt.root.workloads = append(tt.root.workloads[:i], tt.root.workloads[i+1:]...)
			return nil, true
		}
	}
	return nil, false
}

func (tt *TaskTree) AddTaskNode(node *TaskNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for _, twln := range tt.root.workloads {
		if twln.belongToTaskWorkloadNode(node) {
			twln.children = append(twln.children, node)

			// Update database task status when tree changes
			tt.updateTaskStatusInDB()
			return nil, true
		}
	}
	return nil, false
}

func (tt *TaskTree) UpdateTaskNode(oldNode, newNode *TaskNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for _, twln := range tt.root.workloads {
		if twln.belongToTaskWorkloadNode(oldNode) {
			if twln.replaceOldTaskNode(oldNode, newNode) {
				// Update database task status when tree changes
				tt.updateTaskStatusInDB()
				return nil, true
			}
			return nil, false
		}
	}
	return nil, false
}

func (tt *TaskTree) TryDeleteTaskNode(node *TaskNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for _, twln := range tt.root.workloads {
		if twln.belongToTaskWorkloadNode(node) {
			if twln.deleteTaskNode(node) {
				return nil, true
			}
			return nil, false
		}
	}
	return nil, false
}

func (tt *TaskTree) ToTask() (*modeltask.Task, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	task := tt.toTask()
	if task == nil {
		return nil, false
	}
	return task, true
}

func (tt *TaskTree) toTask() *modeltask.Task {
	if tt.root == nil || tt.root.workloads == nil || len(tt.root.workloads) == 0 {
		zlog.SugarWarnf("Failed to convert task tree to task: root is nil or workloads is nil or workloads is empty.")
		return nil
	}

	runtimeInfo := tt.buildRuntimeInfo()

	kwl := tt.root.workloads[0].ref
	ref := tt.root.refs[0]

	opts := &common.TaskBuildOptions{
		ID:        tt.id,
		Workload:  kwl,
		SubTasks:  runtimeInfo.SubTasks,
		Resources: runtimeInfo.Resources,
		NodeNames: runtimeInfo.NodeNames,
		PodCount:  runtimeInfo.PodCount,
		GVK:       inferGVKFromObject(ref),
	}

	task := tt.taskBuilder.BuildTaskFromWorkloadAndRef(kwl, ref, opts)
	return task
}

func (tt *TaskTree) buildRuntimeInfo() *common.TaskRuntimeInfo {
	subTasks := make([]*modeltask.SubTask, 0)
	for _, twn := range tt.root.workloads {
		subTask, err := twn.toSubTask()
		if err != nil {
			zlog.SugarWarnf("Failed to convert workload to subtask: %v", err)
			continue
		}
		subTasks = append(subTasks, subTask)
	}

	totalResources := &modeltask.Resources{
		Requests: *modeltask.NewResourceSpec(),
		Limits:   *modeltask.NewResourceSpec(),
		Used:     modeltask.NewResourceSpec(),
	}
	for _, subTask := range subTasks {
		if subTask.Resources != nil {
			totalResources.Add(subTask.Resources)
		}
	}

	runningNodeNames := make([]string, 0)
	podCount := 0
	for _, subTask := range subTasks {
		runningNodeNames = append(runningNodeNames, subTask.RunningNodeNames()...)
		podCount += len(subTask.Pods)
	}

	return &common.TaskRuntimeInfo{
		Resources: totalResources,
		NodeNames: util.DeduplicateStrings(runningNodeNames),
		SubTasks:  subTasks,
		PodCount:  podCount,
	}
}

func (tt *TaskTree) updateTaskStatusInDB() {
	task := tt.toTask()
	if task == nil {
		zlog.SugarWarnf("Failed to convert task tree to task, cannot update database")
		return
	}
	// only save queue == task
	if task.QueueName != consts.TaskQueueName {
		zlog.SugarDebugf("Skip update task %s status in database because queue is %s", task.ID, task.QueueName)
		return
	}

	daoTask, err := common.ToDAOModel(task)
	if err != nil {
		zlog.SugarErrorf("Failed to convert task to DAO model: %v", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err = dao.MustGetTaskDAO().Save(ctx, daoTask)
	if err != nil {
		zlog.SugarErrorf("Failed to update task status in database: %v", err)
		return
	}

	zlog.SugarDebugf("Successfully updated task %s status to %s in database", task.ID, task.Status)
}
