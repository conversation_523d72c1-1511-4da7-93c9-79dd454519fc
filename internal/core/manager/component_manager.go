package core

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"transwarp.io/mlops/mlops-std/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	_ "k8s.io/client-go/plugin/pkg/client/auth"
	"sigs.k8s.io/yaml"
	"transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/pipeline/internal/config"
	"transwarp.io/mlops/pipeline/internal/core/model"
)

type ComponentManager struct {
	imageTag string
}

type IComponentManager interface {
	GetComponentById(componentId string) (*pb.Component, error)
	GetComponents(ctx context.Context) ([]*pb.Component, error)
}

func NewComponentManager() *ComponentManager {
	componentManager := &ComponentManager{
		imageTag: config.PipeineConfig.MLOPS.ServiceConfig.ImageTag,
	}

	model.ComponentMap = componentManager.getComponents()
	return componentManager
}

func (m *ComponentManager) exportComponents() (string, error) {

	component := &pb.Component{
		Id:         "C00000",
		Name:       "自定义组件",
		NameEn:     "custom component",
		UseDefined: true,
		ComponentSource: &pb.TemplateSource{
			TemplateType: pb.TaskSourceType_PIPELINE,
		},
		Desc:         "自定义组件",
		DescEn:       "batch prediction",
		Inputs:       nil,
		Outputs:      nil,
		Params:       nil,
		NodeSelector: nil,
		Annotations:  nil,
		Labels:       nil,
		Volume:       nil,
		Container: &common.Container{
			Image:                "",
			ResourceRequirements: nil,
			Envs:                 nil,
			Cmds:                 nil,
			MountPaths:           nil,
			Args:                 []string{},
			ImagePullPolicy:      common.Container_PullAlways,
			SecurityContext: &common.SecurityContext{
				Privileged: false,
				RunAsUser:  0,
			},
		},
		ComponentAdvancedConfig: &pb.ComponentAdvancedConfig{
			EnableCached:       false,
			Parallelism:        0,
			ServiceAccountName: "",
			RetryStrategy:      nil,
		},
		HostNetwork: false,
	}
	json, _ := json.Marshal(component)
	yaml, _ := yaml.JSONToYAML(json)

	os.WriteFile(fmt.Sprintf("./core/xml/%s.yaml", component.Id), yaml, 0664)

	return string(yaml), nil
}

func (m *ComponentManager) GetComponentById(componentId string) (*pb.Component, error) {

	component := model.ComponentMap[componentId]
	if component == nil {
		return nil, stderr.ComponentNotFound.Error()
	}
	return component, nil
}

func (m *ComponentManager) getComponents() map[string]*pb.Component {

	componentMap := map[string]*pb.Component{}
	dirPath := "./core/xml"

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(info.Name(), ".yaml") {
			content, err := os.ReadFile(path)
			if err != nil {
				zlog.SugarErrorf("Error reading file: %+v", err)
				return nil
			}
			component := &pb.Component{}
			err = yaml.Unmarshal(content, component)
			if err != nil {
				zlog.SugarDebugln("yaml unmarshal err: %+v", err)
			}
			if strings.Index(component.Container.Image, ":") == -1 {
				component.Container.Image = fmt.Sprintf("%s:%s", component.Container.Image, m.imageTag)
			}
			if config.PipeineConfig.MLOPS.ServiceConfig.DockerImageRepo != "" {
				component.Container.Image = fmt.Sprintf("%s/%s", config.PipeineConfig.MLOPS.ServiceConfig.DockerImageRepo, component.Container.Image)
			}
			componentMap[component.Id] = component
		}
		return nil
	})
	if err != nil {
		zlog.SugarDebugln("filepath walk err: %+v", err)
	}
	return componentMap
}

func (m *ComponentManager) GetComponents(ctx context.Context) ([]*pb.Component, error) {
	lang := util.GetLocaleInCtx(ctx)
	var components []*pb.Component
	for _, component := range model.ComponentMap {
		res := *component
		if lang == "en" {
			res.Name = res.NameEn
			res.Desc = res.DescEn
		}
		components = append(components, &res)
	}
	return components, nil
}
