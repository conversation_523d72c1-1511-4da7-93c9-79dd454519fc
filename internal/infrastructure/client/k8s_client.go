package client

import (
	"context"
	"encoding/json"
	"strings"
	"sync"
	"sync/atomic"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	applisterv1 "k8s.io/client-go/listers/apps/v1"
	batchlisterv1 "k8s.io/client-go/listers/batch/v1"
	corelisterv1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"sigs.k8s.io/yaml"

	"bytes"
	"fmt"
	"io"

	v1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"

	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	Pod      = "Pod"
	Workflow = "Workflow"
)

const (
	K8sClientName     = "k8s"
	CronJobApiVersion = "batch/v1"
	CronJobKind       = "CronJob"
)

var (
	PodGVK = corev1.SchemeGroupVersion.WithKind("Pod")

	DeploymentGVK  = appsv1.SchemeGroupVersion.WithKind("Deployment")
	StatefulSetGVK = appsv1.SchemeGroupVersion.WithKind("StatefulSet")
	DaemonSetGVK   = appsv1.SchemeGroupVersion.WithKind("DaemonSet")
	ReplicaSetGVK  = appsv1.SchemeGroupVersion.WithKind("ReplicaSet")

	JobGVK     = batchv1.SchemeGroupVersion.WithKind("Job")
	CronJobGVK = batchv1.SchemeGroupVersion.WithKind("CronJob")

	k8sClientInstance *K8sClientset
	k8sOnce           sync.Once
)

type K8sClientset struct {
	Clientset *kubernetes.Clientset

	NamespaceLister   corelisterv1.NamespaceLister
	PodLister         corelisterv1.PodLister
	SecretLister      corelisterv1.SecretLister
	DeployLister      applisterv1.DeploymentLister
	StatefulsetLister applisterv1.StatefulSetLister
	ReplicaSetLister  applisterv1.ReplicaSetLister
	DaemonSetLister   applisterv1.DaemonSetLister
	JobLister         batchlisterv1.JobLister
	CronJobLister     batchlisterv1.CronJobLister
	SvcLister         corelisterv1.ServiceLister
	EventLister       corelisterv1.EventLister
	ConfigMapLister   corelisterv1.ConfigMapLister

	PodInformer         cache.SharedIndexInformer
	JobInformer         cache.SharedIndexInformer
	CronJobInformer     cache.SharedIndexInformer
	DeploymentInformer  cache.SharedIndexInformer
	StatefulSetInformer cache.SharedIndexInformer

	sharedInformerFactory informers.SharedInformerFactory

	supportCronJobV1 *atomic.Bool
	mu               sync.Mutex
}

func MustGetK8sClient() *K8sClientset {
	client, err := GetK8sClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get K8s client: %v", err))
	}
	return client
}

func GetK8sClient() (*K8sClientset, error) {
	var initErr error
	k8sOnce.Do(func() {
		config, err := util.GetKubeConfig()
		if err != nil {
			zlog.SugarErrorf("Error building k8s client config: %v", err)
			initErr = err
			return
		}
		client := kubernetes.NewForConfigOrDie(config)

		c := &K8sClientset{
			Clientset:             client,
			sharedInformerFactory: informers.NewSharedInformerFactory(client, DefaultResyncPeriod),
			mu:                    sync.Mutex{},
		}

		c.NamespaceLister = c.sharedInformerFactory.Core().V1().Namespaces().Lister()
		c.PodLister = c.sharedInformerFactory.Core().V1().Pods().Lister()
		c.SecretLister = c.sharedInformerFactory.Core().V1().Secrets().Lister()
		c.SvcLister = c.sharedInformerFactory.Core().V1().Services().Lister()
		c.DeployLister = c.sharedInformerFactory.Apps().V1().Deployments().Lister()
		c.StatefulsetLister = c.sharedInformerFactory.Apps().V1().StatefulSets().Lister()
		c.ReplicaSetLister = c.sharedInformerFactory.Apps().V1().ReplicaSets().Lister()
		c.EventLister = c.sharedInformerFactory.Core().V1().Events().Lister()
		c.ConfigMapLister = c.sharedInformerFactory.Core().V1().ConfigMaps().Lister()
		c.DaemonSetLister = c.sharedInformerFactory.Apps().V1().DaemonSets().Lister()
		c.JobLister = c.sharedInformerFactory.Batch().V1().Jobs().Lister()

		c.PodInformer = c.sharedInformerFactory.Core().V1().Pods().Informer()
		c.JobInformer = c.sharedInformerFactory.Batch().V1().Jobs().Informer()

		c.DeploymentInformer = c.sharedInformerFactory.Apps().V1().Deployments().Informer()
		c.StatefulSetInformer = c.sharedInformerFactory.Apps().V1().StatefulSets().Informer()
		if c.SupportCronJobV1() {
			c.CronJobInformer = c.sharedInformerFactory.Batch().V1().CronJobs().Informer()
			c.CronJobLister = c.sharedInformerFactory.Batch().V1().CronJobs().Lister()
		}

		k8sClientInstance = c
		k8sClientInstance.Start(context.Background())
	})

	return k8sClientInstance, initErr
}

func (k *K8sClientset) Start(ctx context.Context) error {
	stopCh := make(chan struct{})
	go func() {
		<-ctx.Done()
		close(stopCh)
	}()

	// Start informers
	k.sharedInformerFactory.Start(stopCh)

	syncCtx, cancel := context.WithTimeout(ctx, DefaultInformerSyncTimeout) // Use a context derived from the input context
	defer cancel()

	if !k.WaitForCacheSync(syncCtx) {
		return fmt.Errorf("timed out waiting for cache sync.")
	}

	zlog.SugarInfoln("Kubernetes informers cache synced successfully.")
	return nil
}

func (k *K8sClientset) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		k.PodInformer.HasSynced,
		k.JobInformer.HasSynced,
		k.DeploymentInformer.HasSynced,
		k.StatefulSetInformer.HasSynced,
	) && (!k.SupportCronJobV1() || cache.WaitForCacheSync(ctx.Done(),
		k.CronJobInformer.HasSynced,
	))
}

func (k *K8sClientset) GetDiscoveryClient() *discovery.DiscoveryClient {
	return k.Clientset.DiscoveryClient
}

func (c *K8sClientset) SupportCronJobV1() bool {
	defer c.mu.Unlock()
	c.mu.Lock()

	if c.supportCronJobV1 == nil {
		c.supportCronJobV1 = &atomic.Bool{}
		if support := util.SupportAPIResources(c.GetDiscoveryClient(), CronJobApiVersion, CronJobKind); support {
			c.supportCronJobV1.Store(true)
		}
	}

	return c.supportCronJobV1.Load()
}

func (k *K8sClientset) AddPodWatchHandler(handler cache.ResourceEventHandler) {
	k.PodInformer.AddEventHandler(handler)
}

func (k *K8sClientset) GetNamespace(ctx context.Context, name string) (*corev1.Namespace, error) {
	return k.NamespaceLister.Get(name)
}

func (k *K8sClientset) ListPods(ctx context.Context, namespace string) ([]*corev1.Pod, error) {
	pods, err := k.PodLister.Pods(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	for _, pod := range pods {
		pod.SetGroupVersionKind(PodGVK)
	}
	return pods, nil
}

func (k *K8sClientset) GetPod(namespace string, podName string) (*corev1.Pod, error) {
	pod, err := k.PodLister.Pods(namespace).Get(podName)
	if err != nil {
		return nil, err
	}
	pod.SetGroupVersionKind(PodGVK)
	return pod, nil
}

func (k *K8sClientset) GetReplicaSet(namespace string, name string) (*appsv1.ReplicaSet, error) {
	replicaSet, err := k.ReplicaSetLister.ReplicaSets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	replicaSet.SetGroupVersionKind(ReplicaSetGVK)
	return replicaSet, nil
}

func (k *K8sClientset) GetDeployment(namespace string, name string) (*appsv1.Deployment, error) {
	deployment, err := k.DeployLister.Deployments(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	deployment.SetGroupVersionKind(DeploymentGVK)
	return deployment, nil
}

func (k *K8sClientset) GetStatefulSet(namespace string, name string) (*appsv1.StatefulSet, error) {
	statefulset, err := k.StatefulsetLister.StatefulSets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	statefulset.SetGroupVersionKind(StatefulSetGVK)
	return statefulset, nil
}

func (k *K8sClientset) GetDaemonSet(namespace string, name string) (*appsv1.DaemonSet, error) {
	obj, err := k.DaemonSetLister.DaemonSets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	obj.SetGroupVersionKind(DaemonSetGVK)
	return obj, nil
}

func (k *K8sClientset) GetJob(namespace string, name string) (*batchv1.Job, error) {
	obj, err := k.JobLister.Jobs(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	obj.SetGroupVersionKind(JobGVK)
	return obj, nil
}

func (k *K8sClientset) GetCronJob(namespace string, name string) (*batchv1.CronJob, error) {
	obj, err := k.CronJobLister.CronJobs(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	obj.SetGroupVersionKind(CronJobGVK)
	return obj, nil
}

func (k *K8sClientset) GetPodInfo(ctx context.Context, namespace string, podName string) ([]byte, error) {
	pod, err := k.PodLister.Pods(namespace).Get(podName)
	if err != nil {
		return nil, err
	}
	return pod2Yaml(pod)
}

func (k *K8sClientset) GetConfigMapInfo(ctx context.Context, namespace string, cmName string) (*corev1.ConfigMap, error) {
	cm, err := k.ConfigMapLister.ConfigMaps(namespace).Get(cmName)
	if err != nil {
		return nil, err
	}
	return cm, nil
}

func pod2Yaml(pod *corev1.Pod) ([]byte, error) {
	json, _ := json.Marshal(pod)
	yaml, _ := yaml.JSONToYAML(json)
	return yaml, nil
}

func (k *K8sClientset) GetPodEvents(ctx context.Context, namespace, runId, podName string) ([]*corev1.Event, error) {
	res := make([]*corev1.Event, 0)
	event, err := k.EventLister.Events(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	for _, e := range event {
		if !checkEventKind(e, runId, podName) {
			continue
		}
		res = append(res, e)
	}
	return res, nil
}

func checkEventKind(event *corev1.Event, runId, podName string) bool {
	if event == nil {
		return false
	}

	// workflow
	if len(runId) != 0 && Workflow == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, runId) {
		return true
	}

	// pod
	if len(podName) != 0 && Pod == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, podName) {
		return true
	}

	// pod workflow
	if len(runId) != 0 && Pod == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, runId) {
		return true
	}

	return false
}

func (k *K8sClientset) GetServiceByName(serviceName, namespace string) (*corev1.Service, error) {
	svc, err := k.SvcLister.Services(namespace).Get(serviceName)
	if err != nil {
		return nil, err
	}
	return svc, nil
}

func event2Yaml(event []*corev1.Event) ([]byte, error) {
	json, _ := json.Marshal(event)
	yaml, _ := yaml.JSONToYAML(json)
	return yaml, nil
}

func (k *K8sClientset) GetPodEventsYaml(ctx context.Context, namespace string, podName string) ([]byte, error) {
	event, err := k.Clientset.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{
		FieldSelector: fmt.Sprintf("involvedObject.name=%s", podName),
	})
	if err != nil {
		return nil, err
	}
	events := make([]*corev1.Event, len(event.Items))
	for i, e := range event.Items {
		events[i] = &e
	}
	return event2Yaml(events)
}

func (k *K8sClientset) GetPodEventsByLabels(ctx context.Context, namespace string, labelsMap map[string]string) (*corev1.EventList, error) {
	event, err := k.Clientset.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{
		FieldSelector: fields.Everything().String(),
		LabelSelector: labels.SelectorFromSet(labelsMap).String(),
	})
	if err != nil {
		return nil, err
	}
	return event, nil
}

type GetLogsOptions struct {
	TailLines  int64
	LimitBytes int64
}

func (k *K8sClientset) GetContainerLogs(ctx context.Context, namespace string, podName string, containerName string, ops *GetLogsOptions) (io.Reader, error) {
	podLogOps := &corev1.PodLogOptions{
		Container: containerName,
	}
	if ops != nil {
		if ops.LimitBytes > 0 {
			podLogOps.LimitBytes = &ops.LimitBytes
		}
		if ops.TailLines > 0 {
			podLogOps.TailLines = &ops.TailLines
		}
	}
	req := k.Clientset.CoreV1().Pods(namespace).GetLogs(podName, podLogOps)

	stream, err := req.Stream(ctx)
	if err != nil {
		return nil, err
	}
	defer stream.Close()

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, stream)
	if err != nil {
		return nil, err
	}
	return buf, nil
}

func (k *K8sClientset) GetDeploymentByDeploymentName(ctx context.Context, namespace string, name string) (*v1.Deployment, error) {
	req, err := k.Clientset.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	return req, nil
}

func (k *K8sClientset) GetPodsByDeploymentName(ctx context.Context, namespace string, deploymentName string) ([]corev1.Pod, error) {
	pods, err := k.Clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	var podList []corev1.Pod
	for _, pod := range pods.Items {
		if strings.Contains(pod.Name, deploymentName) {
			podList = append(podList, pod)
		}
	}
	if err != nil {
		return nil, err
	}
	return podList, nil
}

func (k *K8sClientset) ListSeldonPods(ctx context.Context, namespace string) ([]corev1.Pod, error) {
	pods, err := k.Clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{LabelSelector: "serving"})
	if err != nil {
		return nil, err
	}
	return pods.Items, nil
}
