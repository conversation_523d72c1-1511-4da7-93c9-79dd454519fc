package client

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"sync"
	"time"

	"transwarp.io/mlops/pipeline/pkg/util/httpclient"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	CASHttpClientName = "cas"

	CASURL = "http://autocv-cas-service:80"
)

var (
	casHTTPClientInstance *CASHttpClient

	casOnce sync.Once
)

type CASHttpClient struct {
	*httpclient.HttpClient
	cache *ResourceCache
}

func MustGetCASHTTPClient() *CASHttpClient {
	client, err := GetCASHTTPClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get CAS client: %v", err))
	}
	return client
}

func GetCASHTTPClient() (*CASHttpClient, error) {
	var initErr error
	casOnce.Do(func() {
		url := CASURL
		if enableDebugStr := os.Getenv("LOCAL_DEBUG_MODEL"); enableDebugStr != "" {
			if enableDebug, err := strconv.ParseBool(enableDebugStr); err == nil || enableDebug {
				if casAddress := os.Getenv("CAS_ADDRESS"); casAddress != "" {
					url = casAddress
				}
			}
		}
		casHTTPClientInstance := &CASHttpClient{
			HttpClient: httpclient.NewHttpClient(url,
				httpclient.WithInsecureSkipVerify()),
		}

		ctx, _ := defaultContext()
		casHTTPClientInstance.cache = NewResourceCache(casHTTPClientInstance, 3*time.Minute)
		casHTTPClientInstance.cache.Start(ctx)

	})

	return casHTTPClientInstance, initErr
}

func (c *CASHttpClient) GetProjectName(projectID string) string {
	if projectID == "" {
		zlog.SugarWarnf("Input param projectID is empty")
		return ""
	}
	return c.cache.GetProjectName(projectID)
}

func (c *CASHttpClient) GetTenantName(tenantID string) string {
	if tenantID == "" {
		zlog.SugarWarnf("Input param tenantID is empty")
		return ""
	}
	return c.cache.GetTenantName(tenantID)
}

type ProjectDetail struct {
	CreateTime    string              `json:"create_time"`
	CreateUser    string              `json:"create_user"`
	Description   string              `json:"description"`
	Disabled      bool                `json:"disabled"`
	Industry      string              `json:"industry"`
	Labels        map[string][]string `json:"labels"`
	Logo          string              `json:"logo"`
	MemberCount   int                 `json:"member_count"`
	Name          string              `json:"name"`
	ProjectID     string              `json:"project_id"`
	ResourceQuota ResourceQuota       `json:"resource_quota"`
	TenantUID     string              `json:"tenant_uid"`
}

type ResourceQuota struct {
	Bandwidth         string `json:"bandwidth"`
	EgressBandwidth   string `json:"egress_bandwidth"`
	GPU               string `json:"gpu"`
	GPUMemory         string `json:"gpu_memory"`
	IngressBandwidth  string `json:"ingress_bandwidth"`
	Knowl             string `json:"knowl"`
	LimitsCPU         string `json:"limits_cpu"`
	LimitsMemory      string `json:"limits_memory"`
	Pods              string `json:"pods"`
	RequestsCPU       string `json:"requests_cpu"`
	RequestsGPU       string `json:"requests_gpu"`
	RequestsGPUMemory string `json:"requests_gpu_memory"`
	RequestsMemory    string `json:"requests_memory"`
	RequestsStorage   string `json:"requests_storage"`
}

func (c *CASHttpClient) GetProjectDetailById(ctx context.Context, projectId string) (*ProjectDetail, error) {
	path := fmt.Sprintf("/api/v1/projmgr/projects/%s", projectId)

	headers, _ := getTokenHeader(ctx)
	result, err := httpclient.GetT[ProjectDetail](ctx, c.HttpClient, path, nil, headers)

	if err != nil {
		return nil, err
	}

	return result, nil
}

type Permission struct {
	ID     int      `json:"id"`
	Code   string   `json:"code"`
	Name   string   `json:"name"`
	Action []string `json:"action"`
}

type UserProfile struct {
	UID              int          `json:"uid"`
	UserName         string       `json:"user_name"`
	FullName         string       `json:"full_name"`
	Email            string       `json:"email"`
	Password         string       `json:"password"`
	UserGroupNames   []string     `json:"user_group_names"`
	CreateUser       string       `json:"create_user"`
	CreateTime       time.Time    `json:"create_time"`
	PlatformRoleName string       `json:"platform_role_name"`
	Permissions      []Permission `json:"permissions"`
	DefaultProject   string       `json:"default_project"`
}

func (c *CASHttpClient) GetUserProfile(ctx context.Context) (*UserProfile, error) {
	path := fmt.Sprintf("/api/v1/usermgr/users/profile")

	headers, _ := getTokenHeader(ctx)
	result, err := httpclient.GetT[UserProfile](ctx, c.HttpClient, path, nil, headers)
	if err != nil {
		return nil, err
	}

	return result, nil
}

type Project struct {
	ProjectID   string    `json:"project_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Labels      []string  `json:"labels,omitempty"`
	CreateUser  string    `json:"createUser"`
	TenantUID   string    `json:"tenant_uid"`
	MemberCount int       `json:"member_count"`
	Disabled    bool      `json:"disabled"`
	Create_user string    `json:"create_user"`
	CreateTime  time.Time `json:"create_time"`
	Logo        string    `json:"logo"`
	Examine     int       `json:"examine"`
}

func (c *CASHttpClient) Listprojects(ctx context.Context) ([]*Project, error) {
	path := fmt.Sprintf("/api/v1/projmgr/projects")
	headers, _ := getTokenHeader(ctx)
	result, err := httpclient.GetT[[]*Project](ctx, c.HttpClient, path, nil, headers)
	if err != nil {
		return nil, err
	}

	return *result, nil
}

type Tenant struct {
	ID   string `json:"tenant_uid"`
	Name string `json:"tenant_name"`
}

func (c *CASHttpClient) ListTenants(ctx context.Context) ([]*Tenant, error) {
	path := fmt.Sprintf("/api/v1/tenants")
	headers, _ := getTokenHeader(ctx)
	result, err := httpclient.GetT[[]*Tenant](ctx, c.HttpClient, path, nil, headers)
	if err != nil {
		return nil, err
	}

	return *result, nil
}

type ResourceCache struct {
	tenantMap       map[string]string // tenantID -> tenantName
	projectMap      map[string]string // projectID -> projectName
	mu              sync.RWMutex
	casClient       *CASHttpClient
	refreshInterval time.Duration
	stopCh          chan struct{}
}

func NewResourceCache(casClient *CASHttpClient, refreshInterval time.Duration) *ResourceCache {
	cache := &ResourceCache{
		tenantMap:       make(map[string]string),
		projectMap:      make(map[string]string),
		casClient:       casClient,
		stopCh:          make(chan struct{}),
		refreshInterval: refreshInterval,
	}

	return cache
}

func (c *ResourceCache) Start(ctx context.Context) {
	ticker := time.NewTicker(c.refreshInterval)
	go func() {
		// Initial refresh
		if err := c.refresh(ctx); err != nil {
			zlog.SugarErrorf("Initial cache refresh failed: %v", err)
		}

		for {
			select {
			case <-ticker.C:
				if err := c.refresh(ctx); err != nil {
					zlog.SugarErrorf("Cache refresh failed: %v", err)
				}
			case <-ctx.Done():
				ticker.Stop()
				close(c.stopCh)
				return
			}
		}
	}()
}

func (c *ResourceCache) refresh(ctx context.Context) error {
	// Get projects list
	projects, err := c.casClient.Listprojects(ctx)
	if err != nil {
		zlog.SugarErrorf("Failed to list projects: %v", err)
		return err
	}
	newProjectMap := make(map[string]string)
	for _, project := range projects {
		newProjectMap[project.ProjectID] = project.Name
	}
	tenants, err := c.casClient.ListTenants(ctx)
	if err != nil {
		zlog.SugarErrorf("Failed to list tenants: %v", err)
		return err
	}
	newTenantMap := make(map[string]string)
	for _, tenant := range tenants {
		newTenantMap[tenant.ID] = tenant.Name
	}

	c.mu.Lock()
	c.projectMap = newProjectMap
	c.tenantMap = newTenantMap
	c.mu.Unlock()

	zlog.SugarInfof("Cache refreshed: %d projects, %d tenants", len(newProjectMap), len(newTenantMap))
	return nil
}

func (c *ResourceCache) GetProjectName(projectID string) string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if name, ok := c.projectMap[projectID]; ok {
		return name
	}
	return projectID // Return ID if name not found
}

func (c *ResourceCache) GetTenantName(tenantID string) string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if name, ok := c.tenantMap[tenantID]; ok {
		return name
	}
	return tenantID // Return ID if name not found
}

func (c *ResourceCache) Stop() {
	close(c.stopCh)
}
