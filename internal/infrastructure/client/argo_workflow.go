package client

import (
	"context"
	"encoding/json"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	clientset "github.com/argoproj/argo-workflows/v3/pkg/client/clientset/versioned"
	versionedclient "github.com/argoproj/argo-workflows/v3/pkg/client/clientset/versioned"
	argoworkflowinforemer "github.com/argoproj/argo-workflows/v3/pkg/client/informers/externalversions"
	listers "github.com/argoproj/argo-workflows/v3/pkg/client/listers/workflow/v1alpha1"
	"github.com/cenkalti/backoff/v4"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/types"
	kubernetesclientschema "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/cache"
	client "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/pipeline/internal/core/consts"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	ArgoWorkflowClientName = "argo-workflow"
)

var (
	WorkflowGVK     = wfv1.SchemeGroupVersion.WithKind("Workflow")
	CronWorkflowGVK = wfv1.SchemeGroupVersion.WithKind("CronWorkflow")

	ArgoWorkflowAPIVersion = "argoproj.io/v1alpha1"
	ArgoWorkflowKind       = "Workflow"

	argoWorkflowClientInstance *ArgoWorkflowClient
	argoWorkflowOnce           sync.Once
)

type ArgoWorkflowClient struct {
	WorkflowLister       listers.WorkflowLister
	WorkflowInformer     cache.SharedIndexInformer
	CronWorkflowLister   listers.CronWorkflowLister
	CronWorkflowInformer cache.SharedIndexInformer
	ClientSet            *clientset.Clientset
	stopCh               chan struct{}

	enabledArgoWorkflow atomic.Bool
	once                sync.Once
}

func MustGetArgoWorkflowClient() *ArgoWorkflowClient {
	argoWorkflowOnce.Do(func() {
		if cli, err := newArgoWorkflowClient(); err != nil {
			panic(err)
		} else {
			argoWorkflowClientInstance = cli
		}
	})
	return argoWorkflowClientInstance
}

func newArgoWorkflowClient() (*ArgoWorkflowClient, error) {
	client := &ArgoWorkflowClient{}
	client.init()
	return client, nil
}

func (c *ArgoWorkflowClient) init() error {
	err := v1alpha1.AddToScheme(kubernetesclientschema.Scheme)
	if err != nil {
		return err
	}
	config, err := client.GetK8sRestConfig()
	if err != nil {
		return stderr.Wrap(err, "init argo cli err :%v", err)
	}
	c.ClientSet, err = versionedclient.NewForConfig(config)
	if err != nil {
		return stderr.Wrap(err, "init argo cli err :%v", err)
	}
	factory := argoworkflowinforemer.NewSharedInformerFactoryWithOptions(
		c.ClientSet,
		DefaultResyncPeriod, // resync 周期
		argoworkflowinforemer.WithTweakListOptions(func(opts *metav1.ListOptions) {
			opts.FieldSelector = fields.Everything().String()
		}),
	)
	c.WorkflowLister = factory.Argoproj().V1alpha1().Workflows().Lister()
	c.WorkflowInformer = factory.Argoproj().V1alpha1().Workflows().Informer()
	c.CronWorkflowLister = factory.Argoproj().V1alpha1().CronWorkflows().Lister()
	c.CronWorkflowInformer = factory.Argoproj().V1alpha1().CronWorkflows().Informer()
	// 启动 informer
	c.stopCh = make(chan struct{}) // 创建 stopCh
	go factory.Start(c.stopCh)
	// 等待缓存同步完成
	if c.IsArgoWorkflowSupported() {
		syncCtx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
		defer cancel()
		if !cache.WaitForCacheSync(syncCtx.Done(),
			c.WorkflowInformer.HasSynced,
			c.CronWorkflowInformer.HasSynced) {
			zlog.SugarErrorf("argo workflow informer failed to sync")
		}
	}
	return nil
}

func (c *ArgoWorkflowClient) IsArgoWorkflowSupported() bool {
	c.once.Do(func() {
		if support := util.SupportAPIResources(MustGetK8sClient().GetDiscoveryClient(), ArgoWorkflowAPIVersion, ArgoWorkflowKind); support {
			c.enabledArgoWorkflow.Store(true)
		} else {
			c.enabledArgoWorkflow.Store(false)
		}
	})

	return c.enabledArgoWorkflow.Load()
}

// TerminateWorkflow sets spec.shutdown to "Terminate" to stop a workflow gracefully
func (c *ArgoWorkflowClient) TerminateWorkflow(name string, namespace string) error {
	patchObj := map[string]interface{}{
		"spec": map[string]interface{}{
			"shutdown": "Terminate",
		},
	}

	patch, err := json.Marshal(patchObj)
	if err != nil {
		return stderr.Internal.Errorf("Unexpected error while marshalling a patch object, err: %+v", err)
	}

	operation := func() error {
		_, err = c.ClientSet.ArgoprojV1alpha1().Workflows(namespace).Patch(context.Background(), name, types.MergePatchType, patch, metav1.PatchOptions{})
		return err
	}

	// Retry up to 10 times with 100ms interval in case of conflicts
	backoffPolicy := backoff.WithMaxRetries(backoff.NewConstantBackOff(100*time.Millisecond), 10)
	return backoff.Retry(operation, backoffPolicy)
}

func (c *ArgoWorkflowClient) RetryWorkflow(workflow *v1alpha1.Workflow) error {

	newWorkflow, podsToDelete, err := formulateRetryWorkflow(workflow)
	if err != nil {
		return stderr.Internal.Errorf("Retry run failed. err: %+v", err)
	}

	if err = deletePods(podsToDelete, workflow.Namespace); err != nil {
		return stderr.Internal.Errorf("Retry run failed. Failed to clean up the failed pods from previous run. err: %+v", err)
	}

	// First try to update workflow
	err = c.syncWorkflow(newWorkflow)
	if err != nil {
		return err
	}
	return nil
}

func (c *ArgoWorkflowClient) syncWorkflow(newWorkflow *v1alpha1.Workflow) error {
	// If fail to get the workflow, return error.
	latestWorkflow, err := c.WorkflowLister.Workflows(newWorkflow.Namespace).Get(newWorkflow.Name)
	if err != nil && !errors.IsNotFound(err) {
		return err
	}
	if errors.IsNotFound(err) {
		newWorkflow.ResourceVersion = ""
		_, err = c.ClientSet.ArgoprojV1alpha1().Workflows(newWorkflow.Namespace).Create(context.Background(), newWorkflow, metav1.CreateOptions{})
		return stderr.Internal.Errorf("Retry run failed. Failed to create the run. Create Error: %+v", err)
	} else {
		newWorkflow.ResourceVersion = latestWorkflow.ResourceVersion
		_, err = c.ClientSet.ArgoprojV1alpha1().Workflows(newWorkflow.Namespace).Update(context.Background(), newWorkflow, metav1.UpdateOptions{})
		return stderr.Internal.Errorf("Retry run failed. Failed to update the run. Update Error: %+v", err)
	}

}

func formulateRetryWorkflow(wf *v1alpha1.Workflow) (*v1alpha1.Workflow, []string, error) {
	switch wf.Status.Phase {
	case v1alpha1.WorkflowFailed, v1alpha1.WorkflowError:
		break
	default:
		return nil, nil, stderr.Internal.Errorf("Workflow must be Failed/Error to retry")
	}

	newWF := wf.DeepCopy()
	// Delete/reset fields which indicate workflow completed
	delete(newWF.Labels, consts.LabelWorkflowCompletedKey)

	newWF.ObjectMeta.Labels[consts.LabelWorkflowPhaseKey] = string(v1alpha1.NodeRunning)
	newWF.Status.Phase = v1alpha1.WorkflowRunning
	newWF.Status.Message = ""
	newWF.Status.FinishedAt = metav1.Time{}
	if newWF.Spec.ActiveDeadlineSeconds != nil && *newWF.Spec.ActiveDeadlineSeconds == 0 {
		// if it was terminated, unset the deadline
		newWF.Spec.ActiveDeadlineSeconds = nil
	}

	// Iterate the previous nodes. If it was successful Pod carry it forward
	newWF.Status.Nodes = make(map[string]v1alpha1.NodeStatus)
	onExitNodeName := wf.ObjectMeta.Name + ".onExit"
	var podsToDelete []string
	for _, node := range wf.Status.Nodes {
		switch node.Phase {
		case v1alpha1.NodeSucceeded, v1alpha1.NodeSkipped:
			if !strings.HasPrefix(node.Name, onExitNodeName) {
				newWF.Status.Nodes[node.ID] = node
				continue
			}
		case v1alpha1.NodeError, v1alpha1.NodeFailed:
			if !strings.HasPrefix(node.Name, onExitNodeName) && node.Type == v1alpha1.NodeTypeDAG {
				newNode := node.DeepCopy()
				newNode.Phase = v1alpha1.NodeRunning
				newNode.Message = ""
				newNode.FinishedAt = metav1.Time{}
				newWF.Status.Nodes[newNode.ID] = *newNode
				continue
			}
			// do not add this status to the node. pretend as if this node never existed.
		default:
			// Do not allow retry of workflows with pods in Running/Pending phase
			return nil, nil, stderr.Internal.Errorf("Workflow cannot be retried with node %s in %s phase", node.ID, node.Phase)
		}
		if node.Type == v1alpha1.NodeTypePod {
			podsToDelete = append(podsToDelete, node.ID)
		}
	}
	return newWF, podsToDelete, nil
}

func deletePods(podsToDelete []string, namespace string) error {
	for _, podId := range podsToDelete {
		err := MustGetK8sClient().Clientset.CoreV1().Pods(namespace).Delete(context.Background(), podId, metav1.DeleteOptions{})
		if err != nil && !errors.IsNotFound(err) {
			return stderr.Internal.Errorf("Failed to delete pods. err: %+v", err)
		}
	}
	return nil
}

func (c *ArgoWorkflowClient) GetWorkflow(namespace, name string) (*wfv1.Workflow, error) {
	wf, err := c.WorkflowLister.Workflows(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	wf.SetGroupVersionKind(WorkflowGVK)
	return wf, nil
}

func (c *ArgoWorkflowClient) GetCronWorkflow(namespace, name string) (*wfv1.CronWorkflow, error) {
	cwf, err := c.CronWorkflowLister.CronWorkflows(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	cwf.SetGroupVersionKind(CronWorkflowGVK)
	return cwf, nil
}
