mlops:
  logConfig:
    path: /var/log/sophon_mlops4/pipeline-server.log
    level: info # default:"info" validate:"regexp=^(info|debug|warn|error)$"`
    format: text # default:"text" validate:"regexp=^(text|json)$"`
    console: true # default:"false"`
    Age:
      Max: 15 # default:"15" validate:"min=1"`
    Size:
      Max: 50 # default:"50" validate:"min=1"`
    Backup:
      Max: 15 # default:"15" validate:"min=0"`
  datasource:
    driver: mysql
    username: root
    password: Warp!CV@2022#
    #host: *************
    #port: 30266
    host: *************
    port: 32441
    dbname: metastore_pipeline_llmops
    maxidle: 20
    maxconn: 10
    notprintsql: false
    notcreatetable: false
  httpServer:
    Port: 8761
    Name: pipeline
  grpcServer:
    Port: 8762
  serviceconfig:
    InstanceId: sophon_mlops8
    ImageTag: master
  etcd:
    endpoints: http://************:2987,http://************:2987,http://************:2987
    dialTimeout: 5
  pipeline:
    #host: *************
    #port: 30266
    host: *************
    port: 32441
    namespace: kubeflow
    maxConcurrency: 3
  warehouse:
    url: tw-node130:9180
    minio:
      endpoint: **************:32443
      accessKey: root
      secretKey: Warp!CV@2022#
      secure: false
      defaultBucket: model-warehouse
  kubeconfig:
    Path: /home/<USER>/workspace/sophon/go/transwarp.io/aip/mlops-pipeline/conf/kubeconfig12417
    KubeConfigPermit: true
  serving:
    grpcServer:
      Host: ************
      Port: 8752