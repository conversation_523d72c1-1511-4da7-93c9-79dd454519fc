package pipeline

import (
	"strconv"
	"strings"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	"github.com/emicklei/go-restful/v3"

	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
	grpc "transwarp.io/mlops/pipeline/api/grpc/pipeline"
	"transwarp.io/mlops/pipeline/api/http/helper"
	core "transwarp.io/mlops/pipeline/internal/core/manager"
)

func (this *RunService) SubmitRun(request *restful.Request, response *restful.Response) {
	body := &pb.SubmitRunBody{}
	request.ReadEntity(body)
	req := &pb.SubmitRunReq{
		ProjectId:     request.QueryParameter("project_id"),
		SubmitRunBody: body,
	}
	res, err := grpc.RunGRPC.SubmitRun(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) SubmitRunV2(request *restful.Request, response *restful.Response) {
	body := &pb.SubmitRunBodyV2{}
	err := request.ReadEntity(body)
	if err != nil {
		util.ErrorResponse(response, stderr.ParamParseFailure.Cause(err, ""), util.GetLocale(request))
		return
	}
	req := &pb.SubmitRunReqV2{
		ProjectId:     request.QueryParameter("project_id"),
		SubmitRunBody: body,
	}
	res, err := grpc.RunGRPC.SubmitRunV2(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) RetryRun(request *restful.Request, response *restful.Response) {
	req := &pb.RetryRunReq{
		RunId: request.PathParameter("run_id"),
	}
	res, err := grpc.RunGRPC.RetryRun(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) listRuns(request *restful.Request) (*pb.ListRunsReq, error) {
	var (
		from int64
		to   int64
		err  error
	)
	fromParam := request.QueryParameter("from")
	if fromParam != "" {
		from, err = strconv.ParseInt(request.QueryParameter("from"), 10, 64)
		if err != nil {
			return nil, stderr.ParamParseFailure.Error("from")
		}
	}
	toParam := request.QueryParameter("to")
	if toParam != "" {
		to, err = strconv.ParseInt(request.QueryParameter("to"), 10, 64)
		if err != nil {
			return nil, stderr.ParamParseFailure.Error("to")
		}
	}
	page, _ := strconv.Atoi(request.QueryParameter("page"))
	pageSize, err := strconv.Atoi(request.QueryParameter("page_size"))
	if err != nil {
		pageSize = 10
	}
	isDesc, _ := strconv.ParseBool(request.QueryParameter("is_desc"))
	sourceType, _ := strconv.Atoi(request.QueryParameter("source_type"))

	var states []pb.Run_Status
	for _, s := range strings.Split(request.QueryParameter("state"), ",") {
		state, err := strconv.Atoi(s)
		if err == nil {
			states = append(states, pb.Run_Status(state))
		}
	}
	var sourceIds []string
	sourceIdParam := request.QueryParameter("source_id")
	if sourceIdParam != "" {
		sourceIds = strings.Split(request.QueryParameter("source_id"), ",")
	}
	return &pb.ListRunsReq{
		ProjectId: request.QueryParameter("project_id"),
		PageReq: &pb.PageReq{
			PageSize: int32(pageSize),
			Page:     int32(page),
			OrderBy:  request.QueryParameter("sort_by"),
			IsDesc:   isDesc,
		},
		Filter: &pb.ListRunsReq_Filter{
			SourceId:   sourceIds,
			SourceType: pb.TaskSourceType(sourceType),
			State:      states,
			From:       from,
			To:         to,
			SourceName: request.QueryParameter("source_name"),
			CreateUser: request.QueryParameter("create_user"),
		},
	}, nil
}

func (this *RunService) ListRuns(request *restful.Request, response *restful.Response) {
	req, err := this.listRuns(request)
	if err != nil {
		util.ErrorResponse(response, err, util.GetLocale(request))
		return
	}
	res, err := grpc.RunGRPC.ListRuns(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) ListRunsV2(request *restful.Request, response *restful.Response) {
	req, err := this.listRuns(request)
	if err != nil {
		util.ErrorResponse(response, err, util.GetLocale(request))
		return
	}
	res, err := grpc.RunGRPC.ListRunsV2(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) GetRunById(request *restful.Request, response *restful.Response) {
	req := &pb.GetRunReq{
		RunId: request.PathParameter("run_id"),
	}
	res, err := grpc.RunGRPC.GetRun(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) GetRunByIdV2(request *restful.Request, response *restful.Response) {
	req := &pb.GetRunReq{
		RunId: request.PathParameter("run_id"),
	}
	res, err := grpc.RunGRPC.GetRunV2(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) DeleteRun(request *restful.Request, response *restful.Response) {
	req := &pb.DeleteRunReq{
		Id: request.PathParameter("run_id"),
	}
	_, err := grpc.RunGRPC.DeleteRun(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, nil, err)
}

func (this *RunService) DeleteRunBatch(request *restful.Request, response *restful.Response) {
	req := &pb.DeleteRunBatchReq{}
	request.ReadEntity(&req)
	res, err := grpc.RunGRPC.DeleteRunBatch(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) TerminateRunById(request *restful.Request, response *restful.Response) {
	req := &pb.TerminateRunReq{
		RunId: request.PathParameter("run_id"),
	}
	res, err := grpc.RunGRPC.TerminateRun(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *RunService) GetRunStepLogs(request *restful.Request, response *restful.Response) {
	reader, err := core.RunMgr.GetRunStepLogs(helper.ContextWithToken(request), request.PathParameter("run_id"), request.PathParameter("node_id"))
	if err != nil {
		zlog.SugarWarnf("handle err : %+v", err)
		util.PostResponse(request, response, nil, nil)
		return
	}

	util.PostResponseWriter(request, response, reader, "", nil)
}

func (this *RunService) GetRunStepArtifact(request *restful.Request, response *restful.Response) {
	reader, fileName, err := core.RunMgr.GetRunStepArtifact(helper.ContextWithToken(request), request.PathParameter("run_id"), request.PathParameter("node_id"), request.PathParameter("artifact"))

	util.PostResponseWriter(request, response, reader, fileName, err)
}

func (this *RunService) GetRunStepPodInfo(request *restful.Request, response *restful.Response) {
	req := &pb.GetRunStepPodInfoReq{
		RunId:  request.PathParameter("run_id"),
		NodeId: request.PathParameter("node_id"),
	}
	// call
	res, err := grpc.RunGRPC.GetRunStepPodInfo(helper.ContextWithToken(request), req)
	if err != nil {
		zlog.SugarErrorf("GetRunStepPodInfo. err is %+v", err)
		util.PostResponse(request, response, nil, nil)
	} else if res != nil {
		util.PostResponse(request, response, string(res.Yaml), err)
	} else {
		util.PostResponse(request, response, nil, nil)
	}
}

func (this *RunService) GetRunStepEvents(request *restful.Request, response *restful.Response) {
	req := &pb.GetRunStepEventsReq{
		RunId:     request.PathParameter("run_id"),
		NodeId:    request.PathParameter("node_id"),
		ProjectId: request.QueryParameter("project_id"),
		TenantId:  request.QueryParameter("tenantId"),
	}
	// call
	res, err := grpc.RunGRPC.GetRunStepEvents(helper.ContextWithToken(request), req)
	if err != nil {
		zlog.SugarErrorf("GetRunStepEvents err is %+v", err)
		util.PostResponse(request, response, nil, nil)
	} else {
		util.PostResponse(request, response, res.Event, nil)
	}
}

func (this *RunService) GetEventsByLabels(request *restful.Request, response *restful.Response) {
	req := &pb.LabelEventsReq{}
	err := request.ReadEntity(&req)
	if err != nil {
		util.PostResponse(request, response, nil, err)
	}
	// call
	res, err := grpc.RunGRPC.GetEventsByLabels(helper.ContextWithToken(request), req)
	if err != nil {
		util.PostResponse(request, response, nil, err)
	} else {
		util.PostResponse(request, response, res.Event, err)
	}
}
